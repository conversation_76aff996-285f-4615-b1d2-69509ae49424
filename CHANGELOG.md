# Changelog

## [1.7.0](https://github.com/luminiris/diverse/compare/v1.6.0...v1.7.0) (2025-08-20)


### Features

* overview page in project detail ([#96](https://github.com/luminiris/diverse/issues/96)) ([ec8a4aa](https://github.com/luminiris/diverse/commit/ec8a4aa2bb08a65e9beb3b3c9522395edcb8cd10))
* pkg upgrade ([#101](https://github.com/luminiris/diverse/issues/101)) ([6fd3b3f](https://github.com/luminiris/diverse/commit/6fd3b3ff25ba486b08cb097ac8603ad38c771609))


### Bug Fixes

* IRR-15 overview S3Store to store tus info file in one folder ([#100](https://github.com/luminiris/diverse/issues/100)) ([783126f](https://github.com/luminiris/diverse/commit/783126fbd6ac313b77611c1a566cd3d1e2dd82b7))
* IRR-17 upload fix ([#103](https://github.com/luminiris/diverse/issues/103)) ([2e5e4f8](https://github.com/luminiris/diverse/commit/2e5e4f8cb480a3c5531828205480021e2c40b25e))
* pkg upgrade minor ([#99](https://github.com/luminiris/diverse/issues/99)) ([920e5dd](https://github.com/luminiris/diverse/commit/920e5ddea140a9ec3b9fcf01fe4b7b4bac9ea87c))

## [1.6.0](https://github.com/luminiris/diverse/compare/v1.5.1...v1.6.0) (2025-08-14)


### Features

* upgrade pkg: @ant-design/nextjs-registry ([#95](https://github.com/luminiris/diverse/issues/95)) ([de369ab](https://github.com/luminiris/diverse/commit/de369ab7a8b4a858deb9c4cd5761550a705cc057))

## [1.5.1](https://github.com/luminiris/diverse/compare/v1.5.0...v1.5.1) (2025-08-12)


### Bug Fixes

* IRR-1 ellipsis project name in project detail header ([#90](https://github.com/luminiris/diverse/issues/90)) ([e5f9af1](https://github.com/luminiris/diverse/commit/e5f9af17aefc2c079a86b17f6efd460eb315f511))
* IRR-10 tree selected style ([#76](https://github.com/luminiris/diverse/issues/76)) ([7c778ad](https://github.com/luminiris/diverse/commit/7c778adbbdbcdaed5ae1caa663c9663d1a06cba6))
* IRR-17 should close modal when cancel btn clicked in uploader list modal ([#92](https://github.com/luminiris/diverse/issues/92)) ([982c490](https://github.com/luminiris/diverse/commit/982c4905f2627f83809108b5b4084d89993ee4af))
* IRR-18 ellpsis remark in project list ([#91](https://github.com/luminiris/diverse/issues/91)) ([cbcf4a6](https://github.com/luminiris/diverse/commit/cbcf4a6c40f9f0d44a6cbcc1edcd646f5821540a))
* IRR-19 name rule of create and update in item and disk tree ([#78](https://github.com/luminiris/diverse/issues/78)) ([4f9a47d](https://github.com/luminiris/diverse/commit/4f9a47dc5cd99ee3ff2b2a1b7f5014233903dcf9))
* IRR-24 ([#73](https://github.com/luminiris/diverse/issues/73)) ([b52e053](https://github.com/luminiris/diverse/commit/b52e05332f06f71bd560bd3c3e269269e48d046e))
* IRR-32 adjust order of menu ([#79](https://github.com/luminiris/diverse/issues/79)) ([8059dc6](https://github.com/luminiris/diverse/commit/8059dc6690f268acb968e154dca3027fb7b0cdf0))
* IRR-33 open project detail by new page ([#80](https://github.com/luminiris/diverse/issues/80)) ([b7dc9fc](https://github.com/luminiris/diverse/commit/b7dc9fcb8242ce8a7bb9bce4de9b2c92f70b8359))
* IRR-34 disk transport action bar style ([#81](https://github.com/luminiris/diverse/issues/81)) ([1ed4083](https://github.com/luminiris/diverse/commit/1ed4083c1b309df436a442135f4492013a8c4712))
* IRR-37 project icon use filled svg ([#82](https://github.com/luminiris/diverse/issues/82)) ([4a1ab25](https://github.com/luminiris/diverse/commit/4a1ab25e0818d5d676bfcf1ae066afa8cc4c18ab))
* IRR-38 reorder the buttons of delete project ([#83](https://github.com/luminiris/diverse/issues/83)) ([9905620](https://github.com/luminiris/diverse/commit/990562081b16a942066cffb88f4311e83aa09f7b))
* IRR-39 cancel upload when cancel button clicked in uploader ([#84](https://github.com/luminiris/diverse/issues/84)) ([2642fcb](https://github.com/luminiris/diverse/commit/2642fcb4e219465ec57a45f52b40d0f997ef5062))
* IRR-41 added link of icon in detail page ([#86](https://github.com/luminiris/diverse/issues/86)) ([1f222d5](https://github.com/luminiris/diverse/commit/1f222d531ac0c0035ee44d4db774e5e65b60576a))
* IRR-43 add confirm when delete node in tree ([#87](https://github.com/luminiris/diverse/issues/87)) ([a27b359](https://github.com/luminiris/diverse/commit/a27b35914af4af50c0f46c0232d0f43bee131b60))
* IRR-45 change cursor to text in disabled item of tree menu ([#88](https://github.com/luminiris/diverse/issues/88)) ([1006418](https://github.com/luminiris/diverse/commit/10064180a18f1b54027029af35ab677eb066841c))
* task-list-layout ([#89](https://github.com/luminiris/diverse/issues/89)) ([fe3dd9f](https://github.com/luminiris/diverse/commit/fe3dd9f94b9e1745ca8f4d03c8ca8a1162657b56))

## [1.5.0](https://github.com/luminiris/diverse/compare/v1.4.0...v1.5.0) (2025-08-10)


### Features

* with db test and coverage ([#74](https://github.com/luminiris/diverse/issues/74)) ([de07c25](https://github.com/luminiris/diverse/commit/de07c25ca13f169a3b7ab47ab7e359f21afef1a9))


### Bug Fixes

* only run ut for release at the moment ([62aba4d](https://github.com/luminiris/diverse/commit/62aba4dd066d45a7d8e34912c9364530d9613b33))
* run ci in main ([9bc0142](https://github.com/luminiris/diverse/commit/9bc0142d1611efa0d2f5f0e2b14f6e7c295c563f))
* run ci in main release ([167292a](https://github.com/luminiris/diverse/commit/167292a3d8f1a09b2c603d20c9a49d2e05efeb4e))
* tree optmize ([#72](https://github.com/luminiris/diverse/issues/72)) ([22b0346](https://github.com/luminiris/diverse/commit/22b034609552e25fe805f412abc66f9325c5ff15))

## [1.4.0](https://github.com/luminiris/diverse/compare/v1.3.2...v1.4.0) (2025-08-07)


### Features

* create item ([#67](https://github.com/luminiris/diverse/issues/67)) ([7c2dfd5](https://github.com/luminiris/diverse/commit/7c2dfd5f4c7cda40c55ca5a225382f9b9994166e))
* delete item ([#69](https://github.com/luminiris/diverse/issues/69)) ([62e8cb0](https://github.com/luminiris/diverse/commit/62e8cb00f455143479aafc0c5aa1b39cc6ab38af))
* item and task ([#55](https://github.com/luminiris/diverse/issues/55)) ([8b53b53](https://github.com/luminiris/diverse/commit/8b53b534136461bbc6f26a52c31df9f9617b1b68))
* item directory tree ([#66](https://github.com/luminiris/diverse/issues/66)) ([aade88e](https://github.com/luminiris/diverse/commit/aade88ec3f57431f50b122e4c0e2cd0d9b5b750c))
* item menus ([#68](https://github.com/luminiris/diverse/issues/68)) ([579a613](https://github.com/luminiris/diverse/commit/579a61394de31bd44aa2d79ab1d31eb88ce86b14))
* move item ([#71](https://github.com/luminiris/diverse/issues/71)) ([b0a45ee](https://github.com/luminiris/diverse/commit/b0a45ee76177cfc5b111f22480a9f63aa0fe37b8))
* show item file size ([#70](https://github.com/luminiris/diverse/issues/70)) ([ecb1638](https://github.com/luminiris/diverse/commit/ecb1638d7f12df252feab9fde30f47b2b605ab0f))


### Bug Fixes

* should throw error when refresh disk ([#64](https://github.com/luminiris/diverse/issues/64)) ([19b2013](https://github.com/luminiris/diverse/commit/19b20138cff0e8ff849ffadbe047ae2637dd7bb4))

## [1.3.2](https://github.com/luminiris/diverse/compare/v1.3.1...v1.3.2) (2025-07-29)


### Bug Fixes

* update node version in docker image for release ([4218938](https://github.com/luminiris/diverse/commit/42189383b018dbc18923f3a99278ec5eb2ad36ce))
* upload progress bar state error fix ([#61](https://github.com/luminiris/diverse/issues/61)) ([8535000](https://github.com/luminiris/diverse/commit/8535000519e8b2f793aab9f85ea30266c8be3f80))

## [1.3.1](https://github.com/luminiris/diverse/compare/v1.3.0...v1.3.1) (2025-07-29)


### Bug Fixes

* env setting ([73ef6a3](https://github.com/luminiris/diverse/commit/73ef6a3dccfb739419ce3eff6c0ad5080f1d45d3))

## [1.3.0](https://github.com/luminiris/diverse/compare/v1.2.0...v1.3.0) (2025-07-28)


### Features

* add task and item db model ([c0c98f4](https://github.com/luminiris/diverse/commit/c0c98f48c04c065f45dfd654798f6ed5a4597270))
* add upload menu in disk tree menus ([#57](https://github.com/luminiris/diverse/issues/57)) ([00b98ee](https://github.com/luminiris/diverse/commit/00b98ee477ddac2c3b32bac3c13e59f208118134))
* add uploader in disk operate & uploader show list default ([#56](https://github.com/luminiris/diverse/issues/56)) ([44815b4](https://github.com/luminiris/diverse/commit/44815b4bdf6c1ff3cc1ac8704d3783c8b53fa2e4))
* disk complete tree ([#50](https://github.com/luminiris/diverse/issues/50)) ([550c421](https://github.com/luminiris/diverse/commit/550c421f6ee274010113850d12b5c5ab2b2f027b))
* disk tree transfer modal ([#52](https://github.com/luminiris/diverse/issues/52)) ([e80d54d](https://github.com/luminiris/diverse/commit/e80d54db1a191661c3c23ddb3e7186eba9177369))
* edit & save for project ([#58](https://github.com/luminiris/diverse/issues/58)) ([8825b90](https://github.com/luminiris/diverse/commit/8825b9052e36d87dc8e8ef1062ef79356b416ddd))
* project list & detail ([#54](https://github.com/luminiris/diverse/issues/54)) ([32e3714](https://github.com/luminiris/diverse/commit/32e3714536b80fecfd3d73a7595a982815898092))
* uploader for disk ([#53](https://github.com/luminiris/diverse/issues/53)) ([fd981d0](https://github.com/luminiris/diverse/commit/fd981d07de4bbcc7a304d5854db4134a539ba151))
* use json ([aec8bd4](https://github.com/luminiris/diverse/commit/aec8bd48b7e7016e3f33fc9698b35d42e034b86f))


### Bug Fixes

* disk bug fix ([#60](https://github.com/luminiris/diverse/issues/60)) ([606f51f](https://github.com/luminiris/diverse/commit/606f51ff16a28bb6ae508168261ad97beabc4f12))
* upload & create dir ([#59](https://github.com/luminiris/diverse/issues/59)) ([e063e29](https://github.com/luminiris/diverse/commit/e063e2929b103a161114c99a0714689b56175f06))
* use remote task instead ray task ([95fdae1](https://github.com/luminiris/diverse/commit/95fdae1d9830421d996a6e223984a4a7abf9d362))

## [1.2.0](https://github.com/luminiris/diverse/compare/v1.1.1...v1.2.0) (2025-07-18)


### Features

* disk svc ([#48](https://github.com/luminiris/diverse/issues/48)) ([d8177b7](https://github.com/luminiris/diverse/commit/d8177b7ce97b7d78ca7541da59fe95b7e1ccced4))
* disk tree ([#49](https://github.com/luminiris/diverse/issues/49)) ([2d00637](https://github.com/luminiris/diverse/commit/2d00637090bbdf88eff43be8169b2f94b450c763))
* error handle & eslint hook ([#42](https://github.com/luminiris/diverse/issues/42)) ([2e0d2c4](https://github.com/luminiris/diverse/commit/2e0d2c40d55134b7450e4dafa94ff1d32b2d4297))
* project list & create ([#45](https://github.com/luminiris/diverse/issues/45)) ([4875761](https://github.com/luminiris/diverse/commit/4875761e5dec7b584220b839f33c8b2da9e32698))
* show-form-modal & error handle change ([#44](https://github.com/luminiris/diverse/issues/44)) ([4686d0b](https://github.com/luminiris/diverse/commit/4686d0b4ee2ef2b1be33b3454073e6132d6859f6))
* tus server ([#46](https://github.com/luminiris/diverse/issues/46)) ([78f8cdb](https://github.com/luminiris/diverse/commit/78f8cdb9959f89a072500b28b105eb6fe50c6a94))
* tus server & s3 client ([#47](https://github.com/luminiris/diverse/issues/47)) ([75083c9](https://github.com/luminiris/diverse/commit/75083c9e0dfa3e3f442a2ed6824b22e852cc6cdb))


### Bug Fixes

* test failed with deps resolver ([819b98d](https://github.com/luminiris/diverse/commit/819b98db395bbab4ec74e3724b0c50d85c9db0cd))

## [1.1.1](https://github.com/luminiris/diverse/compare/v1.1.0...v1.1.1) (2025-07-09)


### Bug Fixes

* use init container instead of run script to do migration when release ([49f8f7a](https://github.com/luminiris/diverse/commit/49f8f7aa221ca12e4e2c7b1b598fcb3b44ccc180))

## [1.1.0](https://github.com/luminiris/diverse/compare/v1.0.1...v1.1.0) (2025-07-09)


### Features

* ui develop, includes: layout, account page. ([#1](https://github.com/luminiris/diverse/issues/1)) ([3029a89](https://github.com/luminiris/diverse/commit/3029a89acf82b63797124a40109871bfac1b64df))
* ui development ([#2](https://github.com/luminiris/diverse/issues/2)) ([3058de9](https://github.com/luminiris/diverse/commit/3058de94eeabff09da7c63a115f11d3dc27b47af))
* use Pro/deepseek-ai/DeepSeek-R1 model ([#13](https://github.com/luminiris/diverse/issues/13)) ([dc64a70](https://github.com/luminiris/diverse/commit/dc64a70cfabea59256d951d52d161f50c7349426))


### Bug Fixes

* add manifest json when release ([34a0792](https://github.com/luminiris/diverse/commit/34a07926c626c4e32503d435c0b8ea4a5da299ce))
* push image when release ([fe5147a](https://github.com/luminiris/diverse/commit/fe5147ac6a4f34a1edc7434ec413d91682bee7f7))
* release test ([159a5a3](https://github.com/luminiris/diverse/commit/159a5a39c7e5cf034481276717dfc35df98a1dea))
* release test ([066204a](https://github.com/luminiris/diverse/commit/066204a467b0fafae318cb387a244d9ddc0eea64))
* test ci/cd ([868db2d](https://github.com/luminiris/diverse/commit/868db2db55b7d9b5ebcd3cf4a33a7bf04d75db36))
* test release ([6e1868a](https://github.com/luminiris/diverse/commit/6e1868ad0fc327faa0484d1f9cfa521b8e22e3a6))
* test release ([552ba25](https://github.com/luminiris/diverse/commit/552ba253cc33f5dc43174de894293b2b99e5f24f))
* test release ([895693c](https://github.com/luminiris/diverse/commit/895693ca0621e9508f49a3a6013325c8ff4750d6))
* test release ([c9575b9](https://github.com/luminiris/diverse/commit/c9575b9c87ad49225ebfa528ca8c48e4582ffdd4))
* test release ([0bbd213](https://github.com/luminiris/diverse/commit/0bbd213856afcf9f14567f5a3d39b7ef8f9f9157))
* test release ([9d2e855](https://github.com/luminiris/diverse/commit/9d2e855bdff0416068ca4cb253b95762b667dd28))
* use deepseek-ai/DeepSeek-R1 ([#12](https://github.com/luminiris/diverse/issues/12)) ([0e62931](https://github.com/luminiris/diverse/commit/0e629311b3c97658b012808acb7743cdcaa25a8b))
* use pr title pattern when release ([31d6019](https://github.com/luminiris/diverse/commit/31d6019a1ed91afaab4a67700b097c59accde30d))
* use scope to avoid warning when release ([eb9d3d1](https://github.com/luminiris/diverse/commit/eb9d3d101a90a2046a925f1932e6e133f0dbc7f8))
* use tag version as the image tag when release ([c87ff5a](https://github.com/luminiris/diverse/commit/c87ff5a501af59e51f4d54ef222e397ea39c1147))

## [1.0.1](https://github.com/luminiris/diverse/compare/v1.0.0...v1.0.1) (2025-07-09)


### Bug Fixes

* add manifest json when release ([34a0792](https://github.com/luminiris/diverse/commit/34a07926c626c4e32503d435c0b8ea4a5da299ce))
* push image when release ([fe5147a](https://github.com/luminiris/diverse/commit/fe5147ac6a4f34a1edc7434ec413d91682bee7f7))
* release test ([159a5a3](https://github.com/luminiris/diverse/commit/159a5a39c7e5cf034481276717dfc35df98a1dea))
* release test ([066204a](https://github.com/luminiris/diverse/commit/066204a467b0fafae318cb387a244d9ddc0eea64))
* test ci/cd ([868db2d](https://github.com/luminiris/diverse/commit/868db2db55b7d9b5ebcd3cf4a33a7bf04d75db36))
* test release ([6e1868a](https://github.com/luminiris/diverse/commit/6e1868ad0fc327faa0484d1f9cfa521b8e22e3a6))
* test release ([552ba25](https://github.com/luminiris/diverse/commit/552ba253cc33f5dc43174de894293b2b99e5f24f))
* test release ([895693c](https://github.com/luminiris/diverse/commit/895693ca0621e9508f49a3a6013325c8ff4750d6))
* test release ([c9575b9](https://github.com/luminiris/diverse/commit/c9575b9c87ad49225ebfa528ca8c48e4582ffdd4))
* test release ([0bbd213](https://github.com/luminiris/diverse/commit/0bbd213856afcf9f14567f5a3d39b7ef8f9f9157))
* test release ([9d2e855](https://github.com/luminiris/diverse/commit/9d2e855bdff0416068ca4cb253b95762b667dd28))
* use pr title pattern when release ([31d6019](https://github.com/luminiris/diverse/commit/31d6019a1ed91afaab4a67700b097c59accde30d))
* use scope to avoid warning when release ([eb9d3d1](https://github.com/luminiris/diverse/commit/eb9d3d101a90a2046a925f1932e6e133f0dbc7f8))
* use tag version as the image tag when release ([c87ff5a](https://github.com/luminiris/diverse/commit/c87ff5a501af59e51f4d54ef222e397ea39c1147))

## [1.0.6](https://github.com/luminiris/diverse/compare/v1.0.5...v1.0.6) (2025-07-09)


### Bug Fixes

* push image when release ([fe5147a](https://github.com/luminiris/diverse/commit/fe5147ac6a4f34a1edc7434ec413d91682bee7f7))
* test release ([6e1868a](https://github.com/luminiris/diverse/commit/6e1868ad0fc327faa0484d1f9cfa521b8e22e3a6))

## [1.0.5](https://github.com/luminiris/diverse/compare/v1.0.4...v1.0.5) (2025-07-09)


### Bug Fixes

* test release ([552ba25](https://github.com/luminiris/diverse/commit/552ba253cc33f5dc43174de894293b2b99e5f24f))

## [1.0.4](https://github.com/luminiris/diverse/compare/v1.0.3...v1.0.4) (2025-07-09)


### Bug Fixes

* test release ([895693c](https://github.com/luminiris/diverse/commit/895693ca0621e9508f49a3a6013325c8ff4750d6))
* test release ([c9575b9](https://github.com/luminiris/diverse/commit/c9575b9c87ad49225ebfa528ca8c48e4582ffdd4))

## [1.0.3](https://github.com/luminiris/diverse/compare/v1.0.2...v1.0.3) (2025-07-09)


### Bug Fixes

* release test ([159a5a3](https://github.com/luminiris/diverse/commit/159a5a39c7e5cf034481276717dfc35df98a1dea))
* release test ([066204a](https://github.com/luminiris/diverse/commit/066204a467b0fafae318cb387a244d9ddc0eea64))
* test release ([0bbd213](https://github.com/luminiris/diverse/commit/0bbd213856afcf9f14567f5a3d39b7ef8f9f9157))

## [1.0.2](https://github.com/luminiris/diverse/compare/v1.0.1...v1.0.2) (2025-07-08)


### Bug Fixes

* test release ([9d2e855](https://github.com/luminiris/diverse/commit/9d2e855bdff0416068ca4cb253b95762b667dd28))

## [1.0.1](https://github.com/luminiris/diverse/compare/v1.0.0...v1.0.1) (2025-07-08)


### Bug Fixes

* test ci/cd ([868db2d](https://github.com/luminiris/diverse/commit/868db2db55b7d9b5ebcd3cf4a33a7bf04d75db36))

## 1.0.0 (2025-07-08)


### Features

* ui develop, includes: layout, account page. ([#1](https://github.com/luminiris/diverse/issues/1)) ([3029a89](https://github.com/luminiris/diverse/commit/3029a89acf82b63797124a40109871bfac1b64df))
* ui development ([#2](https://github.com/luminiris/diverse/issues/2)) ([3058de9](https://github.com/luminiris/diverse/commit/3058de94eeabff09da7c63a115f11d3dc27b47af))
* use Pro/deepseek-ai/DeepSeek-R1 model ([#13](https://github.com/luminiris/diverse/issues/13)) ([dc64a70](https://github.com/luminiris/diverse/commit/dc64a70cfabea59256d951d52d161f50c7349426))


### Bug Fixes

* use deepseek-ai/DeepSeek-R1 ([#12](https://github.com/luminiris/diverse/issues/12)) ([0e62931](https://github.com/luminiris/diverse/commit/0e629311b3c97658b012808acb7743cdcaa25a8b))
