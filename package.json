{"name": "diverse", "version": "0.2.0", "private": true, "scripts": {"dev": "IRS_HTTP_REQ_TIMEOUT=0 next dev --turbopack", "build": "IRS_HTTP_REQ_TIMEOUT=0 next build", "start": "IRS_HTTP_REQ_TIMEOUT=0 next start", "lint": "next lint && tsc", "test": "vitest", "test:coverage": "vitest --coverage", "ut": "vitest --project unit-test", "dbt": "vitest --project db-test", "postinstall": "prisma generate", "route-path": "pnpx nextjs-paths generate -f route-path.ts"}, "dependencies": {"@47ng/cloak": "^1.2.0", "@ai-sdk/openai-compatible": "^1.0.10", "@ai-sdk/provider": "^2.0.0", "@ai-sdk/react": "^2.0.18", "@ant-design/colors": "^7.2.1", "@ant-design/nextjs-registry": "^1.1.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@assistant-ui/react": "^0.10.43", "@assistant-ui/react-ai-sdk": "^1.0.3", "@assistant-ui/react-markdown": "^0.10.9", "@atlaskit/pragmatic-drag-and-drop": "^1.7.4", "@auth/core": "^0.40.0", "@auth/prisma-adapter": "^2.10.0", "@aws-sdk/client-s3": "^3.873.0", "@ctrl/tinycolor": "^4.1.0", "@deck.gl-community/editable-layers": "^9.1.1", "@deck.gl/core": "^9.1.14", "@deck.gl/geo-layers": "^9.1.14", "@deck.gl/layers": "^9.1.14", "@luma.gl/core": "^9.1.9", "@luma.gl/engine": "^9.1.9", "@luma.gl/shadertools": "^9.1.9", "@prisma/client": "^6.14.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@reduxjs/toolkit": "^2.8.2", "@restatedev/restate-sdk": "^1.8.3", "@restatedev/restate-sdk-clients": "^1.8.3", "@svgr/webpack": "^8.1.0", "@turf/bbox": "^7.2.0", "@turf/meta": "^7.2.0", "@tus/s3-store": "^2.0.0", "@tus/server": "^2.3.0", "@uppy/core": "^4.5.2", "@uppy/dashboard": "^4.4.3", "@uppy/drag-drop": "^4.2.2", "@uppy/file-input": "^4.2.2", "@uppy/progress-bar": "^4.3.2", "@uppy/react": "^4.5.2", "@uppy/tus": "^4.3.2", "@zumer/snapdom": "^1.9.9", "ai": "^5.0.18", "ali-oss": "^6.23.0", "aliyun-api-gateway": "^1.1.6", "antd": "^5.27.1", "authing-node-sdk": "^4.0.1", "big.js": "^7.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "es-toolkit": "^1.39.10", "lucide-react": "^0.539.0", "mermaid": "^11.10.0", "nanoid": "^5.1.5", "next": "15.4.6", "next-auth": "5.0.0-beta.29", "next-navigation-guard": "^0.2.0", "next-safe-action": "^8.0.10", "node-forge": "^1.3.1", "nuqs": "^2.4.3", "p-retry": "7.0.0-0", "proxy-agent": "^5.0.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-shiki": "^0.7.3", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "shiki": "^3.11.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "three": "^0.179.1", "wechatpay-nextjs-v3": "^1.0.1", "wretch": "^2.11.0", "zod": "^4.0.17", "zod-config": "^1.3.0"}, "devDependencies": {"@chax-at/transactional-prisma-testing": "^1.3.0", "@eslint/eslintrc": "^3.3.1", "@next/env": "^15.5.0", "@next/eslint-plugin-next": "^15.5.0", "@restatedev/restate-sdk-zod": "^1.8.3", "@tailwindcss/postcss": "^4.1.12", "@testing-library/dom": "^10.4.1", "@testing-library/react": "^16.3.0", "@types/ali-oss": "^6.16.11", "@types/big.js": "^6.2.2", "@types/node": "^24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/three": "^0.179.0", "@vitejs/plugin-react": "^5.0.1", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9.33.0", "eslint-config-next": "^15.5.0", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "prisma": "^6.14.0", "tailwindcss": "^4.1.12", "typescript": "^5.9.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "vitest-mock-extended": "^3.1.0"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}