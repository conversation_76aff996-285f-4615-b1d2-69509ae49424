diff --git a/dist/server/lib/start-server.js b/dist/server/lib/start-server.js
index e2d723189a00208947f8ce61ebee4448e0a0e217..88e62671426992d51a8fec9bcbd4539ca3b5eb09 100644
--- a/dist/server/lib/start-server.js
+++ b/dist/server/lib/start-server.js
@@ -242,6 +242,12 @@ async function startServer(serverOptions) {
     if (keepAliveTimeout) {
         server.keepAliveTimeout = keepAliveTimeout;
     }
+    // To allow for configurable request timeout (i.e. for file uploads)
+    // https://github.com/vercel/next.js/discussions/71651
+    if (process.env.IRS_HTTP_REQ_TIMEOUT) {
+        server.requestTimeout = parseInt(process.env.IRS_HTTP_REQ_TIMEOUT, 10);
+        console.log(`> next server requestTimeout set to ${server.requestTimeout}ms`);
+    }
     server.on('upgrade', async (req, socket, head)=>{
         try {
             await upgradeHandler(req, socket, head);
