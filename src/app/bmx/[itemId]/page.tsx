import { ImageEditor } from "@/app-ui/components/image/editor";
import { Metadata } from "next";

export type ParamsType = {
  params: Promise<{ itemId: string }>;
};
type Props = ParamsType & {};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { itemId } = await params;

  return {
    title: itemId,
  };
}

export default async function BMXPage({ params }: Props) {
  const { itemId } = await params;

  return <ImageEditor imgInfo={{ name: "xxx", id: "id", imgMeta: {} }} />;
}
