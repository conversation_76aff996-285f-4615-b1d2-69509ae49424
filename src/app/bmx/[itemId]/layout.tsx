import { SiderMenu } from "@/app-ui/components/image/sider-menu";
import { ToolBar } from "@/app-ui/components/image/tool-bar/tool-bar";
import { ParamsType } from "@/app/bmx/[itemId]/page";
import { Skeleton } from "antd";
import Sider from "antd/es/layout/Sider";
import { Content } from "antd/es/layout/layout";
import { Suspense } from "react";

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode;
} & ParamsType) {
  const { itemId } = await params;
  return (
    <>
      <div className="flex flex-col h-screen">
        <div className="border-b">
          <div className="flex">
            <div className="flex-auto">
              <Suspense fallback={<Skeleton></Skeleton>}>
                <ToolBar itemId={itemId} />
              </Suspense>
            </div>
          </div>
        </div>
        <div className="flex flex-auto">
          <Sider width={128} className="bg-white">
            <SiderMenu />
          </Sider>
          <Content>{children}</Content>
        </div>
      </div>
    </>
  );
}
