import { PrjResource } from "@/app-ui/components/project/resource";
import { IngestTask } from "@/app-ui/components/task/ingest";
import { Splitter } from "antd";

export default async function IngestPage({
  params,
}: {
  params: Promise<{ projectId: string }>;
}) {
  const { projectId } = await params;

  return (
    <div>
      <Splitter className="h-screen">
        <Splitter.Panel min={400} max={800} defaultSize={400}>
          <div className="flex flex-col h-full">
            <div className="flex-auto border-b">
              <PrjResource projectId={projectId} />
            </div>
          </div>
        </Splitter.Panel>
        <Splitter.Panel>
          <IngestTask projectId={projectId} />
        </Splitter.Panel>
      </Splitter>
    </div>
  );
}
