import { di } from "@/app-ui/di";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { QueryTasksInputSchema, TasksRes } from "@/lib/query/svc";
import { NextRequest, NextResponse } from "next/server";

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const querySvc = await di.getQuerySvc();
  const params = req.nextUrl.searchParams;

  const length = params.get("length");
  const parsedInput = QueryTasksInputSchema.parse({
    projectId: params.get("projectId"),
    status: params.get("status") ?? undefined,
    sortBy: params.get("sortBy") ?? undefined,
    length: length ? Number(length) : undefined,
    order: params.get("order") ?? undefined,
  });

  const tasks = await querySvc.tasks(parsedInput);

  return NextResponse.json(tasks as TasksRes[]);
});
