import { di } from "@/app-ui/di";
import { createUnifiedAPIRouteHandler } from "@/lib/infra/nextjs/router-handler";
import { QueryItemsInputSchema, ItemsRes } from "@/lib/query/svc";
import { NextRequest, NextResponse } from "next/server";

export const GET = createUnifiedAPIRouteHandler(async (req: NextRequest) => {
  await di.ensureLogin();
  const querySvc = await di.getQuerySvc();
  const params = req.nextUrl.searchParams;

  const length = params.get("length");

  const parsedInput = QueryItemsInputSchema.parse({
    projectId: params.get("projectId"),
    length: length ? Number(length) : undefined,
    sortBy: params.get("sortBy") ?? undefined,
  });

  const items = await querySvc.items(parsedInput);
  return NextResponse.json(items as ItemsRes[]);
});
