import {
  Attribute,
  DefaultProps,
  Layer,
  LayerContext,
  UpdateParameters,
  picking,
  project32,
} from "@deck.gl/core";

import { Texture, TextureFormat } from "@luma.gl/core";
import { Geometry, Model } from "@luma.gl/engine";
import type { ShaderModule } from "@luma.gl/shadertools";

const uniformBlock = `\
uniform irsUniforms {
  vec4 color;
  vec2 contrastLimits;
  float gamma;
} irs;
`;

type IrsBindingProps = {
  irsTexture: Texture;
};

type IrsUniformProps = {
  color: [number, number, number, number];
  contrastLimits: [number, number];
  gamma: number;
};

type IrsProps = IrsBindingProps & IrsUniformProps;

const irmUniforms: ShaderModule<IrsProps, IrsUniformProps, IrsBindingProps> = {
  // name here will generate nameUniforms for uniform structs,
  // so make sure they are under this convenstion
  name: "irs",
  // vs: uniformBlock,
  fs: uniformBlock,

  uniformTypes: {
    color: "vec4<f32>",
    contrastLimits: "vec2<f32>",
    gamma: "f32",
  },
};

const vs = `\
#version 300 es
#define SHADER_NAME irs-vertex-shader

in vec2 texCoords;
in vec3 positions;
in vec3 positions64Low;

const vec3 pickingColor = vec3(1.0, 0.0, 0.0);

out vec2 vTexCoord;

void main(void) {
  geometry.worldPosition = positions;
  geometry.uv = texCoords;
  geometry.pickingColor = pickingColor;

  gl_Position = project_position_to_clipspace(positions, positions64Low, vec3(0.), geometry.position);
  
  DECKGL_FILTER_GL_POSITION(gl_Position, geometry);
  
  vTexCoord = texCoords;

  vec4 color = vec4(0.);
  DECKGL_FILTER_COLOR(color, geometry);
}
`;

const fs = `\
#version 300 es
#define SHADER_NAME irs-fragment-shader

precision highp float;
//precision highp int;
precision highp usampler2D;
precision highp sampler2D;

uniform sampler2D irsTexture;
// uniform usampler2D irsTexture;

in vec2 vTexCoord;

out vec4 fragColor;

void main() {

  float intensity = float(texture(irsTexture, vTexCoord).r);
  float intensity_nomalized = (intensity - irs.contrastLimits[0]) / max(0.0005, (irs.contrastLimits[1] - irs.contrastLimits[0]));
  intensity_nomalized = max(0., intensity_nomalized);  
  fragColor = intensity_nomalized * irs.color;
  fragColor.rgb = pow(fragColor.rgb, vec3(1.0/irs.gamma));
  fragColor[3] = irs.color[3];
  // fragColor.rgba = vec4(1.,intensity,0.,1.);


  geometry.uv = vTexCoord;
  DECKGL_FILTER_COLOR(fragColor, geometry);
}
`;

export class TileImage {
  constructor(readonly buffer: ArrayBuffer) {}
  width(): number {
    return new DataView(this.buffer).getUint16(2, true);
  }

  height(): number {
    return new DataView(this.buffer).getUint16(0, true);
  }

  data(): ArrayBuffer {
    return this.buffer.slice(4);
  }
}
export type MagFilter = "linear" | "nearest";

type IntensityLayerProps = {
  name: string;
  dtype: string;
  image: TileImage;
  // this can be ignore when compare prop change, as one layer is bind to some bounds permanently
  bounds: number[];
  color: [number, number, number, number];
  contrastLimits: [number, number];
  gamma: number;
  magFilter: MagFilter;
};

class IntensityLayer extends Layer<IntensityLayerProps> {
  initializeState(context: LayerContext): void {
    // @ts-expect-error
    const { gl }: { gl: WebGL2RenderingContext } = context.device;
    gl.pixelStorei(gl.PACK_ALIGNMENT, 1);
    gl.pixelStorei(gl.UNPACK_ALIGNMENT, 1);

    const attributeMg = this.getAttributeManager()!;

    attributeMg.remove(["instancePickingColors"]);

    attributeMg.add({
      positions: {
        size: 3,
        type: "float64",
        fp64: this.use64bitPositions(),
        update: this.calculatePositions,
        noAlloc: true,
      },
    });

    const model = new Model(context.device, {
      id: this.props.id,
      fs: fs,
      vs: vs,
      modules: [project32, picking, irmUniforms as ShaderModule],
      bufferLayout: this.getAttributeManager()?.getBufferLayouts(),
      geometry: new Geometry({
        topology: "triangle-strip",
        attributes: {
          texCoords: {
            size: 2,
            value: new Float32Array([0, 1, 0, 0, 1, 1, 1, 0]),
          },
        },
      }),
    });

    this.setState({
      model: model,
    });
  }

  calculatePositions(attribute: Attribute) {
    const positions = new Float64Array(12);
    const { bounds } = this.props;
    // bounds as [minX, minY, maxX, maxY]
    /*
      (minX0, maxY3) ---- (maxX2, maxY3)
             |                  |
             |                  |
             |                  |
      (minX0, minY1) ---- (maxX2, minY1)
   */
    positions[0] = bounds[0];
    positions[1] = bounds[1];
    positions[2] = 0;

    positions[3] = bounds[0];
    positions[4] = bounds[3];
    positions[5] = 0;

    positions[6] = bounds[2];
    positions[7] = bounds[1];
    positions[8] = 0;

    positions[9] = bounds[2];
    positions[10] = bounds[3];
    positions[11] = 0;

    attribute.setData(positions);
  }

  is_dtype_supported(dtype: string) {
    return ["uint16", "uint8", "float32"].includes(dtype);
  }

  generate_texture_prop(buffer: ArrayBufferLike) {
    let arrayData;
    let format: TextureFormat;
    switch (this.props.dtype) {
      case "uint16":
        arrayData = new Float32Array(new Uint16Array(buffer));
        format = "r32float";
        break;
      case "uint8":
        arrayData = new Float32Array(new Uint8Array(buffer));
        format = "r32float";
        break;
      case "float32":
        arrayData = new Float32Array(buffer);
        format = "r32float";
        break;
      default:
        throw new Error("unsupport dtype: " + this.props.dtype);
    }

    return {
      data: arrayData,
      format,
    };
  }

  getRawValue(xInRaw: number, yInRaw: number): number {
    const { image } = this.props;
    if (!image) {
      return 0;
    }
    const index = yInRaw * image.width() + xInRaw;
    const { data: rawData } = this.generate_texture_prop(image.data());
    const rawValue = rawData[index];
    return rawValue;
  }

  getTexture(
    image: TileImage,
    ctx: LayerContext,
    magFilter: MagFilter = "nearest"
  ) {
    return ctx.device.createTexture({
      width: image.width(),
      height: image.height(),
      mipmaps: false,
      sampler: {
        minFilter: "linear",
        magFilter: magFilter,
        addressModeU: "clamp-to-edge",
        addressModeV: "clamp-to-edge",
      },
      ...this.generate_texture_prop(image.data()),
    });
  }

  is_ok_to_draw() {
    return this.props.image && this.is_dtype_supported(this.props.dtype);
  }

  updateState(params: UpdateParameters<Layer<IntensityLayerProps>>): void {
    // If the layer does need to be updated, layer.updateState() is called
    // to perform any necessary operation before the layer is rendered.
    // This usually involves recalculating an attribute
    // by calling state.attributeManager.invalidate and
    // updating uniforms by calling model.setUniforms. By default,
    // when props.data changes, all attributes are invalidated and recalculated.
    // and should be invalid bounds and color when needed
    if (params.changeFlags.propsChanged && this.is_ok_to_draw()) {
      // console.log(params.changeFlags, this.props.magFilter);
      // return;
      this.getModels().forEach((x) => {
        const intensityProps: IrsProps = {
          color: params.props.color,
          contrastLimits: params.props.contrastLimits,
          irsTexture: this.getTexture(
            params.props.image,
            this.context,
            params.props.magFilter
          ),
          gamma: params.props.gamma,
        };
        x.shaderInputs.setProps({ irs: intensityProps });
      });
    }
  }

  draw(opts: any): void {
    this.is_ok_to_draw() && super.draw(opts);
  }
}

const defautlProps: DefaultProps<IntensityLayerProps> = {
  color: { type: "color", value: [0, 0, 0] },
  bounds: { type: "object", value: [], ignore: true },
  contrastLimits: { type: "array", value: [0, 255], compare: true },
  image: { type: "object", value: undefined as any, async: true },
  gamma: { type: "number", value: 1 },
  magFilter: "nearest",
};
IntensityLayer.defaultProps = defautlProps;
IntensityLayer.layerName = "IntensityLayer";
export default IntensityLayer;
