import { type FetchTileFn } from "@/lib/apis/neon/api";
import { ImgMeta } from "@/lib/apis/neon/image-endpoints";
import IntensityLayer, {
  type MagFilter,
  type TileImage,
} from "@/lib/layers/intensity-layer";
import type { LayersList } from "@deck.gl/core";
import {
  TileLayer,
  _Tileset2D,
  type TileLayerProps,
  type _Tile2DHeader,
  type _TileLoadProps,
} from "@deck.gl/geo-layers";

class SkipRemoveTopCache extends Map<string, _Tile2DHeader> {
  constructor() {
    super();
  }

  delete(key: string): boolean {
    const x = this.get(key);
    if (x && !x.parent) {
      return false;
    } else {
      return super.delete(key);
    }
  }
}

export class HackTileset2D extends _Tileset2D {
  constructor(props: any) {
    super(props);
    // @ts-expect-error
    this._cache = new SkipRemoveTopCache();
  }
}

export type TileData = { channelId: number; image: TileImage }[];

async function getTileData(
  fetchTileFn: FetchTileFn,
  tile: _TileLoadProps,
  source: string,
  channelIds: number[],
  version: string
): Promise<TileData> {
  let index = tile.index;
  let images = channelIds.map((channelId) => {
    const req = {
      id: source,
      x: index.x,
      y: index.y,
      z: index.z,
      channel_id: channelId,
      version: version,
    };

    const image = fetchTileFn(req, { signal: tile.signal });

    return {
      channelId,
      image,
    };
  });

  if (images.length > 0) {
    await Promise.any(images.map((x) => x.image));
  }

  return images as any;
}

function renderSubLayers(
  ops: Options,
  {
    id,
    tile,
  }: {
    id: string;
    tile: _Tile2DHeader<TileData>;
  }
) {
  const layers: LayersList = [];

  const [[left, top], [right, bottom]] = tile.boundingBox;
  const bounds = [
    left,
    Math.min(ops.imgMeta.base_y, bottom),
    Math.min(ops.imgMeta.base_x, right),
    top,
  ];

  for (const channelByTile of tile.content ?? []) {
    const channelId = channelByTile.channelId;
    const channelInfo = ops.channels.find((x) => x.id === channelId)!;
    if (channelInfo) {
      const layer = new IntensityLayer({
        id:
          id +
          ":intensity-channel:" +
          channelId +
          ":" +
          ops.imgMeta.data_version,
        image: channelByTile.image,
        dtype: ops.imgMeta.dtype,
        contrastLimits: [channelInfo.min, channelInfo.max],
        color: channelInfo.color,
        pickable: ops.pickable,
        bounds: bounds,
        name: channelInfo.name,
        parameters: {
          blendColorSrcFactor: "src-alpha",
          blendColorDstFactor: "dst-alpha",
          blendColorOperation: "add",
        },
        gamma: ops.gamma,
        magFilter: ops.magFilter,
      });
      layers.push(layer);
    }
  }

  // layers.push(
  //   new PathLayer({
  //     id: id + ":path-layer",
  //     data: [
  //       {
  //         path: [
  //           [left, bottom],
  //           [right, bottom],
  //           [right, top],
  //           [left, top],
  //           [left, bottom],
  //         ],
  //       },
  //     ],
  //     widthMaxPixels: 1,
  //     getColor: [0, 255, 0, 255],
  //   })
  // );

  return layers;
}

export interface ChannelInfo {
  id: number;
  name: string;
  min: number;
  max: number;
  color: [number, number, number, number];
}

type Options = {
  imgMeta: ImgMeta;
  channels: ChannelInfo[];
  source: string;
  pickable?: boolean;
  tileFetcher: FetchTileFn;
  gamma: number;
  magFilter?: MagFilter;
};

export function createTileLayer(
  ops: Options,
  innerProps: Partial<TileLayerProps<TileData>>
) {
  const { max_level, min_level, base_x, base_y, tile_size, data_version } =
    ops.imgMeta;

  const minZoom = min_level - max_level;
  const extent = [0, 0, base_x, base_y];
  const channelIds = ops.channels.map((x) => x.id);
  const getTileDataTriggerKey = [...channelIds]
    .sort((a, b) => a - b)
    .join()
    .concat("-", data_version);
  const layer = new TileLayer<
    TileData,
    { channels: ChannelInfo[]; gamma: number; magFilter: MagFilter }
  >({
    refinementStrategy: "no-overlap",
    maxRequests: 25,
    minZoom: minZoom,
    maxZoom: 0,
    extent: extent,
    tileSize: tile_size,
    renderSubLayers: (x) => renderSubLayers(ops, { ...x }),
    getTileData: (x) =>
      getTileData(ops.tileFetcher, x, ops.source, channelIds, data_version),
    updateTriggers: {
      getTileData: [getTileDataTriggerKey],
    },
    channels: ops.channels,
    gamma: ops.gamma,
    magFilter: ops.magFilter,
    ...innerProps,
  });

  return layer;
}
