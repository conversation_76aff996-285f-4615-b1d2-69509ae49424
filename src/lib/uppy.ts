import { cacheLazySync } from "@/lib/lazy";

import Uppy, { type Body, type Meta } from "@uppy/core";
import Tus, { type TusOpts } from "@uppy/tus";

const onBeforeRequest: TusOpts<Meta, Body>["onBeforeRequest"] = (req, file) => {
  req.setHeader(
    "irs-meta",
    Buffer.from(JSON.stringify(file.meta)).toString("base64")
  );
};

export const getUppy = cacheLazySync(() => {
  const uppy = new Uppy().use(Tus, {
    endpoint: "/api/upload",
    onBeforeRequest,
  });
  return uppy;
});
