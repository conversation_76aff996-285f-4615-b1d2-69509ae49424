import type {
  TransactionsSourceInfo,
  TransactionsSourceType,
} from "@/lib/domain/atp/m";
import {
  get_root_iid_path,
  getPiidPath,
  ItemTypeFlag,
} from "@/lib/domain/item/svc";
import {
  convertTaskRemoteStateToStatus,
  TaskRemoteInnerState,
  TaskStatus,
  type TaskArgs,
  type TaskType,
} from "@/lib/domain/task/svc";
import type { Operator } from "@/lib/domain/user/m";
import type { Prisma, PrismaClient } from "@prisma/client";
import { z } from "zod";

export const ListItemsByLevelInputSchema = z.object({
  pid: z.string().optional(),
  projectId: z.string(),
});

export const QueryItemsInputSchema = z.object({
  projectId: z.string(),
  sortBy: z.enum(["created_at", "updated_at"]).optional(),
  length: z.number().min(1).optional(),
});

export const QueryTasksInputSchema = z.object({
  projectId: z.string(),
  status: z.enum(TaskStatus).optional(),
  sortBy: z.enum(["start_at", "finished_at"]).optional(),
  length: z.number().min(1).optional(),
  order: z.enum(["asc", "desc"]).optional(),
});

export type ListItemsByLevelInput = z.infer<typeof ListItemsByLevelInputSchema>;

export type QueryItemsInput = z.infer<typeof QueryItemsInputSchema>;

export type QueryTasksInput = z.infer<typeof QueryTasksInputSchema>;

export class QuerySvc {
  constructor(
    private readonly operator: Operator,
    private readonly prisma: PrismaClient
  ) {}

  async rechargeOrders(): Promise<RechargeOrdersRes[]> {
    const recharges = await this.prisma.recharge.findMany({
      where: {
        user_id: this.operator.id,
      },
      select: {
        id: true,
        amount: true,
        status: true,
        atp: true,
        pay_transaction_id: true,
        pay_time: true,
        pay_callback_time: true,
        created_at: true,
      },
      orderBy: {
        created_at: "desc",
      },
    });

    return recharges.map((x) => {
      return {
        ...x,
        amount: x.amount.toNumber(),
        atp: x.atp.toNumber(),
      };
    });
  }

  async projects(): Promise<ProjectsRes[]> {
    const projects = await this.prisma.project.findMany({
      where: {
        created_by_id: this.operator.id,
        deleted_at: 0,
      },
      orderBy: {
        created_at: "desc",
      },
    });

    return projects;
  }

  async transactions(): Promise<TransactionsRes[]> {
    const transactions = await this.prisma.transaction.findMany({
      where: {
        user_id: this.operator.id,
      },
      select: {
        id: true,
        atp: true,
        source_type: true,
        source_info: true,
        created_at: true,
      },
    });

    return transactions.map((x) => {
      return {
        ...x,
        atp: x.atp.toNumber(),
      } as TransactionsRes;
    });
  }

  async listItemsByLevel({
    pid,
    projectId,
  }: ListItemsByLevelInput): Promise<ListItemsByLevelRes[]> {
    const piid_path = await getPiidPath(this.prisma, { pid, projectId });

    const items = await this.prisma.item.findMany({
      where: {
        piid_path,
        deleted_at: 0,
      },
      orderBy: {
        created_at: "desc",
      },
      omit: {
        iid: true,
        piid_path: true,
      },
    });

    return items;
  }

  async items({
    projectId,
    sortBy = "created_at",
    length,
  }: QueryItemsInput): Promise<ItemsRes[]> {
    const items = await this.prisma.item.findMany({
      where: {
        piid_path: {
          startsWith: get_root_iid_path(projectId),
        },
        deleted_at: 0,
        type_flag: ItemTypeFlag.File,
      },
      take: length,
      orderBy: {
        [sortBy]: "desc",
      },
      omit: {
        iid: true,
        piid_path: true,
      },
    });

    return items;
  }

  async tasks({
    projectId,
    status,
    sortBy = "start_at",
    length,
    order = "desc",
  }: QueryTasksInput): Promise<TasksRes[]> {
    let statusFilter: Prisma.TaskWhereInput = {};

    if (status) {
      switch (status) {
        case TaskStatus.Failed:
          statusFilter = {
            remote_task_status: { in: [TaskRemoteInnerState.FAILED] },
          };
          break;
        case TaskStatus.Finished:
          statusFilter = {
            remote_task_status: { in: [TaskRemoteInnerState.FINISHED] },
          };
          break;
        case TaskStatus.Running:
          statusFilter = {
            finished_at: null,
            remote_task_status: { not: null },
          };
          break;
      }
    }

    const tasks = await this.prisma.task.findMany({
      where: {
        project_id: projectId,
        ...statusFilter,
      },
      omit: {
        extra: true,
      },
      orderBy: {
        [sortBy]: order,
      },
      take: length,
    });

    const res: TasksRes[] = tasks.map((task) => ({
      id: task.id,
      created_by_id: task.created_by_id,
      task_status: task.remote_task_status
        ? convertTaskRemoteStateToStatus(
            task.remote_task_status as TaskRemoteInnerState
          )
        : null,
      project_id: task.project_id,
      task_type: task.task_type as TaskType,
      task_args: task.task_args as TaskArgs,
      remote_task_id: task.remote_task_id,
      start_at: task.start_at,
      finished_at: task.finished_at,
      error: task.error,
    }));

    return res;
  }

  async task(taskId: string): Promise<TaskDetailRes | undefined> {
    const task = await this.prisma.task.findFirst({
      where: {
        id: taskId,
      },
    });

    if (!task) {
      return;
    }

    const res: TaskDetailRes = {
      id: task.id,
      project_id: task.project_id,
      task_type: task.task_type as TaskType,
      task_args: task.task_args as TaskArgs,
      remote_task_id: task.remote_task_id,
      remote_task_status: task.remote_task_status,
      start_at: task.start_at,
      finished_at: task.finished_at,
      error: task.error,
    };

    return res;
  }
}
export type RechargeOrdersRes = {
  id: string;
  amount: number;
  status: string;
  atp: number;
  pay_transaction_id: string | null;
  pay_time: Date | null;
  pay_callback_time: Date | null;
  created_at: Date;
};

export type TransactionsRes = {
  id: string;
  atp: number;
  created_at: Date;
  source_type: TransactionsSourceType;
  source_info: TransactionsSourceInfo;
};

export type ProjectsRes = {
  id: string;
  name: string;
  remark: string | null;
  created_at: Date;
  updated_at: Date;
};

export type ListItemsByLevelRes = {
  id: string;
  name: string;
  type_flag: number;
  size: number | null;
  task_id: string | null;
  created_by_id: string;
  created_at: Date;
  updated_by_id: string;
  updated_at: Date;
  deleted_at: number;
};

export type ItemsRes = {
  id: string;
  name: string;
  type_flag: number;
  size: number | null;
  task_id: string | null;
  created_by_id: string;
  created_at: Date;
  updated_by_id: string;
  updated_at: Date;
  deleted_at: number;
};

export type TaskDetailRes = {
  id: string;
  project_id: string;
  task_type: string;
  task_args: TaskArgs;
  remote_task_id: string | null;
  remote_task_status: string | null;
  start_at: Date;
  finished_at: Date | null;
  error: string | null;
};

export type TasksRes = {
  error: string | null;
  id: string;
  created_by_id: string;
  task_status: TaskStatus | null;
  project_id: string;
  task_type: TaskType;
  task_args: TaskArgs;
  remote_task_id: string | null;
  start_at: Date;
  finished_at: Date | null;
};
