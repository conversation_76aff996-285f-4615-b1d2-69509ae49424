import type { NeonApiEndpointBuilder } from "@/lib/apis/neon/api";
import { GetTaskInfoRes } from "@/lib/apis/neon/task-endpoints";

const imageEndpoints = (builder: NeonApiEndpointBuilder) => {
  return {
    getImgMeta: builder.query<GetImgMetaRes, string>({
      query: (id) => ({ url: "image/meta", params: { id } }),
      providesTags: (res, error, id) => [{ type: "image-meta", id }],
    }),
    getImgOriginalMeta: builder.query<GetOriginalMetaRes, string>({
      query: (id) => ({ url: "image/original-meta", params: { id } }),
      providesTags: (res, error, id) => [{ type: "image-original-meta", id }],
    }),
    saveImgMeta: builder.mutation<void, SaveTileViewInfoReq>({
      query: (req) => ({ url: "image/meta", body: req, method: "PUT" }),
      invalidatesTags: (res, err, req) => {
        return [{ type: "image-meta", id: req.id }];
      },
    }),
    askRescaleIntensity: builder.mutation<void, AskRescaleIntensityReq>({
      query: (req) => ({
        url: "image/ask-rescale-intensity",
        body: req,
        method: "POST",
      }),
      invalidatesTags: (res, err, req) => {
        return [{ type: "image-meta", id: req.id }];
      },
    }),
    askClipIntensity: builder.mutation<void, AskClipIntensityReq>({
      query: (req) => ({
        url: "image/ask-clip-intensity",
        body: req,
        method: "POST",
      }),
      invalidatesTags: (res, err, req) => {
        return [{ type: "image-meta", id: req.id }];
      },
    }),
    askFlip: builder.mutation<void, AskFlipReq>({
      query: (req) => ({
        url: "image/ask-flip",
        body: req,
        method: "POST",
      }),
      invalidatesTags: (res, err, req) => {
        return [{ type: "image-meta", id: req.id }];
      },
    }),
    getMinMaxByQuantile: builder.query<
      GetMinMaxByQuantileRes,
      GetMinMaxByQuantileReq
    >({
      query: (req) => ({ url: "image/min-max-by-quantile", params: req }),
    }),
    askBitDepthConvert: builder.mutation<void, AskBitDepthConvertReq>({
      query: (req) => ({
        url: "image/ask-bit-depth-convert",
        body: req,
        method: "POST",
      }),
    }),
    askSaveAs: builder.mutation<void, AskSaveAsReq>({
      query: (req) => ({
        url: "image/ask-save-as",
        body: req,
        method: "POST",
      }),
    }),
  };
};

export const SupportedSaveAsType = <const>["irs", "tiff", "jpg"];
export type SaveAsType = (typeof SupportedSaveAsType)[number];

export interface XYRegion {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface AskSaveAsReq {
  id: string;
  channels: {
    id: number;
    name: string;
  }[];
  split_channels: boolean;
  tgt_folder_id: string;
  folder_name: string;
  tgt_type: SaveAsType;
  region?: XYRegion;
}

export const SupportedBitDepth = <const>["uint8", "uint16", "float32"];

export type BitDepth = (typeof SupportedBitDepth)[number];

export interface AskBitDepthConvertReq {
  id: string;
  tgt_bit_depth: BitDepth;
  tgt_folder_id: string;
  tgt_img_name: string;
}

export type RatioType = "intensity" | "pixel";

export interface GetMinMaxByQuantileReq {
  id: string;
  channel_id: number;
  min_quantile?: number;
  max_quantile?: number;
  in_unique: boolean;
}

export interface GetMinMaxByQuantileRes {
  min?: number;
  max?: number;
}
interface ChannelArg {
  id: number;
  min: number;
  max: number;
}
interface AskRescaleIntensityReq {
  id: string;
  channels: ChannelArg[];
}

interface AskClipIntensityReq {
  id: string;
  channels: ChannelArg[];
}

interface AskFlipReq {
  id: string;
  flip_axes: { dims: "X" | "Y" };
}

interface ChannelMeta {
  id: number;
  name?: string;
  color?: string;
  view_min?: number;
  view_max?: number;
  view_shown?: boolean;
}

interface SaveTileViewInfoReq {
  id: string;
  channels?: ChannelMeta[];
  phys_x?: number;
  phys_x_unit?: string;
  phys_y?: number;
  phys_y_unit?: string;
  view_gamma?: number;
}

interface Channel {
  id: number;
  name: string;
  min: number;
  max: number;
  view_min?: number;
  view_max?: number;
  view_shown?: boolean;
  color?: string;
}

export interface ImgMeta {
  tile_size: number;
  base_x: number;
  base_y: number;
  min_level: number;
  max_level: number;
  dtype: string;
  channels: Channel[];
  axes: string;
  shape: number[];
  phys_x?: number;
  phys_x_unit?: string;
  phys_y?: number;
  phys_y_unit?: string;
  phys_z?: number;
  phys_z_unit?: string;
  data_version: string;
  view_gamma?: number;
}

export interface GetImgMetaRes {
  iid_path: string;
  id: string;
  pid: string;
  name: string;
  size?: number;
  task?: GetTaskInfoRes;
  img_meta: ImgMeta;
}

interface GetOriginalMetaRes {
  meta: {};
}

export interface GetHistogramReq {
  id: string;
}

export interface GetHistogramRes {
  channels: ChannelHistogram[];
}

interface ChannelHistogram {
  id: number;
  hist: number[];
  bins: number[];
}

export default imageEndpoints;
