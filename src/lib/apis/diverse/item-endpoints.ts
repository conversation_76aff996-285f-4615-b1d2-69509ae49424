import { DiverseApiEndpointBuilder } from "@/lib/apis/diverse/api";
import type {
  ListItemsByLevelInput,
  ListItemsByLevelRes,
  QueryItemsInput,
  ItemsRes,
} from "@/lib/query/svc";

export const itemEndpoints = (builder: DiverseApiEndpointBuilder) => {
  return {
    listItem: builder.query<ListItemsByLevelRes[], ListItemsByLevelInput>({
      query: (req) => {
        return {
          url: "item/iterate",
          params: req,
        };
      },
      providesTags: (_, err, req) => {
        return err ? [] : [{ type: "item", id: req.pid || req.projectId }];
      },
    }),

    getItems: builder.query<ItemsRes[], QueryItemsInput>({
      query: (req) => {
        return {
          url: "item",
          params: req,
        };
      },
      providesTags: (_, err, req) => {
        return err
          ? []
          : [
              {
                type: "item",
                id: `${req.projectId}-${req.sortBy || "default"}-${
                  req.length || "all"
                }`,
              },
            ];
      },
    }),
  };
};
