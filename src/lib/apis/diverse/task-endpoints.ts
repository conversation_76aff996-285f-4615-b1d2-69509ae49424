import { DiverseApiEndpointBuilder } from "@/lib/apis/diverse/api";
import type { QueryTasksInput, TasksRes } from "@/lib/query/svc";

export const taskEndpoints = (builder: DiverseApiEndpointBuilder) => {
  return {
    getTasks: builder.query<TasksRes[], QueryTasksInput>({
      query: (req) => {
        return {
          url: "task",
          params: req,
        };
      },
      providesTags: (_, err, req) => {
        return err
          ? []
          : [
              {
                type: "item",
                id: `${req.projectId}-${req.status}-${req.sortBy}-${req.order}-${req.length}`,
              },
            ];
      },
    }),
  };
};
