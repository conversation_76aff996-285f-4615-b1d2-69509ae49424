import { StorePaths } from "@/lib/domain/disk/svc";
import { S3Store } from "@tus/s3-store";

export class IrsS3Store extends S3Store {
  protected infoKey(id: string): string {
    const infoKey = transformDiskPathToInfoPath(id);
    return `${infoKey}.info`;
  }
}

function transformDiskPathToInfoPath(diskPath: string) {
  const reg = new RegExp(
    `^(${StorePaths.project}\/[^\/]+\/)${StorePaths.disk}`
  );
  return diskPath.replace(reg, `$1${StorePaths.info}`);
}

if (import.meta.vitest) {
  const { describe, it, expect } = import.meta.vitest;

  describe("transformDiskPathToInfoPath", () => {
    it("transform disk path to info path", () => {
      const diskPath =
        "project/prjId/disk/AFolder/project/3s1a/disk/user/my-file.txt";
      const infoPath = transformDiskPathToInfoPath(diskPath);
      expect(infoPath).toBe(
        "project/prjId/disk-upload-tus-info/AFolder/project/3s1a/disk/user/my-file.txt"
      );
    });
  });
}
