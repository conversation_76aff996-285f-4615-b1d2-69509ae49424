import { AppConfig } from "@/lib/app-config";
import { DomainError } from "@/lib/domain/common/error";
import { DiskPath } from "@/lib/domain/disk/svc";
import {
  createItem,
  get_item_iid_path,
  get_root_iid_path,
  getItem,
  ItemSvc,
  ItemTypeFlag,
} from "@/lib/domain/item/svc";
import { Operator } from "@/lib/domain/user/m";
import { PrismaClient, Task } from "@prisma/client";
import z from "zod";
import wretch, { type Wretch } from "wretch";
import dayjs from "dayjs";
import { RestateClient } from "@/lib/infra/restate/client";

export const AddTaskInputSchema = z.object({
  projectId: z.string(),
  taskReq: z.any(),
});

export const RerunTaskInputSchema = z.object({
  projectId: z.string(),
  taskId: z.string(),
});

export enum TaskType {
  ingest = "ingest",
  rescale_intensity = "rescale-intensity",
  clip_intensity = "clip-intensity",
  register = "register",
  export = "export",
  bit_depth_convert = "bit-depth-convert",
  save_as = "save-as",
  flip = "flip",
  merge_channels = "merge-channels",
}

export enum TaskRemoteInnerState {
  NIL = "NIL",
  PENDING_ARGS_AVAIL = "PENDING_ARGS_AVAIL",
  PENDING_NODE_ASSIGNMENT = "PENDING_NODE_ASSIGNMENT",
  PENDING_OBJ_STORE_MEM_AVAIL = "PENDING_OBJ_STORE_MEM_AVAIL",
  PENDING_ARGS_FETCH = "PENDING_ARGS_FETCH",
  SUBMITTED_TO_WORKER = "SUBMITTED_TO_WORKER",
  PENDING_ACTOR_TASK_ARGS_FETCH = "PENDING_ACTOR_TASK_ARGS_FETCH",
  PENDING_ACTOR_TASK_ORDERING_OR_CONCURRENCY = "PENDING_ACTOR_TASK_ORDERING_OR_CONCURRENCY",
  RUNNING = "RUNNING",
  RUNNING_IN_RAY_GET = "RUNNING_IN_RAY_GET",
  RUNNING_IN_RAY_WAIT = "RUNNING_IN_RAY_WAIT",
  FINISHED = "FINISHED",
  FAILED = "FAILED",
}

export enum TaskStatus {
  Running = "Running",
  Finished = "Finished",
  Failed = "Failed",
}

export type TaskReq = IngestTaskReq;
export type TaskArgs = TaskReq["taskArgs"];

type IngestTaskReq = {
  taskType: "ingest";
  taskArgs: {
    projectId: string;
    sourcePath: string;
    outputFolderId?: string;
    outputName: string;
  };
};

type IngestExtra = {
  decodedItemId: string;
};

type TaskExtra = IngestExtra;

export class TaskSvc {
  private remoteTaskApi: Wretch;
  constructor(
    private readonly itemSvc: ItemSvc,
    private readonly prisma: PrismaClient,
    appConfig: AppConfig,
    private readonly rsClient: RestateClient
  ) {
    this.remoteTaskApi = wretch(appConfig.REMOTE_TASK_API_URL);
  }

  async runIngest({ task, operator }: { task: Task; operator: Operator }) {
    const taskId = task.id;

    await this.itemSvc.clearTaskItems(operator, { taskId });

    const { projectId, sourcePath, outputFolderId, outputName } =
      task.task_args as IngestTaskReq["taskArgs"];

    let output_folder_iid_path = get_root_iid_path(projectId);
    if (outputFolderId) {
      const output_folder = await getItem(this.prisma, {
        itemId: outputFolderId,
        typeFlag: ItemTypeFlag.Folder,
      });

      output_folder_iid_path = get_item_iid_path(output_folder);
    }

    const decodedItem = await createItem(this.prisma, {
      piid_path: output_folder_iid_path,
      typeFlag: ItemTypeFlag.File,
      name: outputName,
      taskId: taskId,
      operator: operator,
    });

    const sourceOssKey = new DiskPath(
      { projectId: projectId },
      sourcePath
    ).toOSSKey();

    const decodedItemTdbGroupUri = this.itemSvc.getTdbGroupUri(decodedItem);

    const remoteTaskId = await this.startRemoteTask({
      task_id: taskId,
      remote_task_req: {
        task_type: "ingest",
        task_args: {
          source_path: sourceOssKey,
          output_path: decodedItemTdbGroupUri,
        },
      },
    });

    const extra = {
      decodedItemId: decodedItem.id,
    } as IngestExtra;

    await this.bindRemoteTaskAndUpdateStatus({
      taskId: taskId,
      remoteTaskId: remoteTaskId,
      extra: extra,
    });
  }

  async afterIngest({ task }: { task: Task }) {
    const extra = task.extra as IngestExtra;
    const decodedItemId = extra.decodedItemId;
    await this.itemSvc.updateItemSize({ itemId: decodedItemId });
  }

  async add_task_and_run(
    operator: Operator,
    {
      projectId,
      taskReq,
    }: {
      projectId: string;
      taskReq: TaskReq;
    }
  ) {
    const task = await addTask(this.prisma, {
      projectId: projectId,
      taskReq: taskReq,
      operator: operator,
    });

    await this.runTask({ task, operator });
    return task;
  }

  async rerunTaskById(operator: Operator, { taskId }: { taskId: string }) {
    const task = await this.prisma.task.findFirst({
      where: {
        id: taskId,
      },
    });

    if (task) {
      await this.runTask({ task, operator });
    }
  }

  async runTask({ task, operator }: { task: Task; operator: Operator }) {
    const taskId = task.id;
    try {
      await resetTaskStateToStart(this.prisma, {
        task_id: task.id,
        operator,
      });

      switch (task.task_type as TaskType) {
        case TaskType.ingest:
          await this.runIngest({ task, operator });
          break;
        default:
          throw new DomainError("task_type_not_supported", {
            task_type: task.task_type,
          });
      }
    } catch (error) {
      let errorMsg = "系统异常";
      console.error("run task failed: ", taskId, error);

      if (error instanceof DomainError) {
        errorMsg = error.toString();
      }

      try {
        await recordTaskError(this.prisma, {
          task_id: taskId,
          errorMsg,
        });
      } catch (error) {
        console.error("record task error failed in run_task.", taskId, error);
      }
    }
  }

  async startRemoteTask({
    task_id,
    remote_task_req,
  }: {
    task_id: string;
    remote_task_req: RemoteTaskReq;
  }) {
    try {
      const remoteTaskId = await this.remoteTaskApi
        .url(`/start-task/${task_id}`)
        .post(remote_task_req)
        .json<string>();

      return remoteTaskId;
    } catch (err) {
      console.error(`failed to call start-ray-task`, {
        task_id,
        remote_task_req,
        err,
      });

      throw new DomainError("start_task_api_failed", { cause: String(err) });
    }
  }

  async processAfterTaskDone({ taskId }: { taskId: string }) {
    const task = await this.prisma.task.findFirst({
      where: {
        id: taskId,
      },
    });
    if (!task) {
      return;
    }

    const taskType = task.task_type;
    try {
      switch (taskType as TaskType) {
        case TaskType.ingest:
          await this.afterIngest({ task });
          break;
        default:
          // todo: raise error to restate?
          throw new Error(
            `task_type ${taskType} is not supported for processing after task done for ${taskId}`
          );
      }
    } catch (error) {
      console.error("processAfterTaskDone failed: ", error, task);
      // todo: raise error to restate?
    }
  }

  async bindRemoteTaskAndUpdateStatus({
    taskId,
    remoteTaskId,
    extra,
  }: {
    taskId: string;
    remoteTaskId: string;
    extra?: TaskExtra;
  }) {
    await bindRemoteTask(this.prisma, {
      task_id: taskId,
      remote_task_id: remoteTaskId,
      extra: extra,
    });

    await this.rsClient.taskSvc.sendClient.updateRemoteTaskStatus({
      taskId,
      remoteTaskId,
    });
  }

  async updateRemoteTaskStatus({
    taskId,
    remoteTaskId,
  }: {
    taskId: string;
    remoteTaskId: string;
  }): Promise<{ keepPolling: boolean }> {
    const task = await this.prisma.task.findFirst({
      where: {
        id: taskId,
        remote_task_id: remoteTaskId,
      },
    });

    if (!task) {
      return { keepPolling: false };
    }

    const timeoutSecondForLostState = 30;
    const taskDurationInSecond = dayjs().diff(task.start_at, "second");

    const remoteTaskState = await this.getRemoteTaskState({ remoteTaskId });

    if (remoteTaskState == null) {
      if (taskDurationInSecond > timeoutSecondForLostState) {
        await recordTaskError(this.prisma, {
          task_id: taskId,
          errorMsg: "计算服务重启，请重试任务",
        });

        return { keepPolling: false };
      }

      return { keepPolling: true };
    }

    const remoteTaskStatus = remoteTaskState.state;

    if (remoteTaskStatus == "FINISHED" || remoteTaskStatus == "FAILED") {
      const errorMsg = remoteTaskState.error_message;
      await recordTaskError(this.prisma, {
        task_id: taskId,
        errorMsg: errorMsg,
        remote_task_status: remoteTaskStatus,
      });

      return { keepPolling: false };
    } else {
      await this.prisma.task.update({
        where: {
          id: taskId,
        },
        data: {
          remote_task_status: remoteTaskStatus,
        },
      });

      return { keepPolling: true };
    }
  }

  async getRemoteTaskState({ remoteTaskId }: { remoteTaskId: string }) {
    const state = await this.remoteTaskApi
      .url(`/task-state/${remoteTaskId}`)
      .get()
      .json<{
        state: TaskRemoteInnerState;
        creation_time_ms?: number;
        start_time_ms?: number;
        end_time_ms?: number;
        error_message?: string;
      } | null>();
    return state;
  }
}

async function resetTaskStateToStart(
  prisma: PrismaClient,
  {
    task_id,
    operator,
  }: {
    task_id: string;
    operator: Operator;
  }
) {
  await prisma.task.update({
    where: {
      id: task_id,
    },
    data: {
      created_by_id: operator.id,
      start_at: new Date(),
      remote_task_id: null,
      remote_task_status: null,
      finished_at: null,
      error: null,
    },
  });
}

async function recordTaskError(
  prisma: PrismaClient,
  {
    task_id,
    errorMsg,
    remote_task_status,
  }: {
    task_id: string;
    errorMsg?: string;
    remote_task_status?: string;
  }
) {
  await prisma.task.update({
    where: {
      id: task_id,
    },
    data: {
      remote_task_status: remote_task_status,
      finished_at: new Date(),
      error: errorMsg,
    },
  });
}

async function bindRemoteTask(
  prisma: PrismaClient,
  {
    task_id,
    remote_task_id,
    extra,
  }: {
    task_id: string;
    remote_task_id: string;
    extra?: TaskExtra;
  }
) {
  await prisma.task.update({
    where: {
      id: task_id,
    },
    data: {
      remote_task_id: remote_task_id,
      extra: extra,
    },
  });
}

async function addTask(
  prisma: PrismaClient,
  {
    projectId,
    taskReq,
    operator,
  }: {
    projectId: string;
    taskReq: TaskReq;
    operator: Operator;
  }
) {
  const { taskType, taskArgs } = taskReq;
  const task = await prisma.task.create({
    data: {
      project_id: projectId,
      task_type: taskType,
      task_args: taskArgs,
      created_by_id: operator.id,
    },
  });
  return task;
}

type RemoteTaskReq = RemoteTaskIngestReq;

type RemoteTaskIngestReq = {
  task_type: "ingest";
  task_args: {
    source_path: string;
    output_path: string;
  };
};

export function convertTaskRemoteStateToStatus(state: TaskRemoteInnerState) {
  switch (state) {
    case TaskRemoteInnerState.FINISHED:
      return TaskStatus.Finished;
    case TaskRemoteInnerState.FAILED:
      return TaskStatus.Failed;
    default:
      return TaskStatus.Running;
  }
}
