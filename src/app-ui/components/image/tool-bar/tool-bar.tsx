"use client";

import { neonApi } from "@/lib/apis/neon/api";
import { ImgMeta } from "@/lib/apis/neon/image-endpoints";
import { PermissionFlag } from "@/lib/apis/neon/items-endpoints";
import { useAppDispatch, useAppSelector } from "@/lib/app-store/hooks";
import imageSlice, {
  selectEnabledTools,
  type ToolId,
} from "@/lib/app-store/image-slice";
import styles from "@/lib/components/image/image.module.css";
import { SaveAsMenu } from "@/lib/components/image/operation/save-as";
import SaveImageViewBtn from "@/lib/components/image/tool-bar/save-btn";
import { EnsurePermission } from "@/lib/components/utils/ensure-admin";
import { IrsIcon } from "@/lib/components/utils/icon";
import {
  Button,
  Divider,
  Dropdown,
  Flex,
  Skeleton,
  type MenuProps,
} from "antd";
import Text from "antd/es/typography/Text";
import Link from "next/link";
import type { ReactNode } from "react";
import { AiOutlineMenu } from "react-icons/ai";
import { CiPickerEmpty } from "react-icons/ci";

export function ToolBar({ itemId }: { itemId: string }) {
  const { currentData: imgMetaRes } = neonApi.useGetImgMetaQuery(itemId);

  if (!imgMetaRes) {
    return <Skeleton active />;
  }

  return (
    <>
      <Flex align="center" style={{ width: 100 }}>
        <LeftTopMenus
          imgMeta={imgMetaRes.img_meta}
          pid={imgMetaRes.pid}
          id={imgMetaRes.id}
          iidPath={imgMetaRes.iid_path}
          imgName={imgMetaRes.name}
        />
      </Flex>

      <Flex flex={"1 1 auto"}>
        <ToolsWithInfo id={imgMetaRes.id} name={imgMetaRes.name} />
      </Flex>
    </>
  );
}

function ToolIcon({ toolId, icon }: { toolId: ToolId; icon: ReactNode }) {
  const dispatch = useAppDispatch();
  const enabledTools = useAppSelector(selectEnabledTools);

  const onClick = () => {
    if (enabledTools.includes(toolId)) {
      dispatch(imageSlice.actions.disableTool(toolId));
    } else {
      dispatch(imageSlice.actions.enableTool(toolId));
    }
  };

  const classNmae = enabledTools.includes(toolId) ? styles.btnEnabled : "";

  return (
    <Button type="text" className={classNmae} icon={icon} onClick={onClick} />
  );
}

function Tools() {
  return (
    <>
      <Button
        type="text"
        className={styles.btnActive}
        icon={<IrsIcon name="Hand" className="text-xl" />}
      />
      {/* <ToolIcon
        toolId="select-draw"
        icon={<IrsIcon name="Arrow" className="text-xl" />}
      /> */}
      <ToolIcon
        toolId="draw-rect"
        icon={<IrsIcon name="Square" className="text-xl" />}
      />
      <Divider type="vertical" style={{ borderColor: "rgba(5,5,5,0.2)" }} />
      <ToolIcon
        toolId="scale-bar"
        icon={<IrsIcon name="Ruler" className="text-xl" />}
      />
      <ToolIcon toolId="info-picker" icon={<CiPickerEmpty size={20} />} />
      <ToolIcon
        toolId="mini-map"
        icon={<IrsIcon name="Aim" className="text-xl" />}
      />
    </>
  );
}

export function ToolsWithInfo({ name, id }: { name: string; id: string }) {
  return (
    <>
      <Flex
        align="start"
        justify="center"
        flex={"0 0 auto"}
        style={{ paddingLeft: 20, width: 280 }}
        vertical
      >
        <Text ellipsis={{ tooltip: true }} type="secondary">
          {name}
        </Text>
        <Text type="secondary" copyable>
          {id}
        </Text>
      </Flex>
      <Flex flex={"auto"} align="center" justify="center" gap={"small"}>
        <Tools />
      </Flex>
    </>
  );
}

function LeftTopMenus({
  imgMeta,
  pid,
  id,
  iidPath,
  imgName,
}: {
  imgMeta: ImgMeta;
  pid: string;
  id: string;
  iidPath: string;
  imgName: string;
}) {
  const items: MenuProps["items"] = [
    {
      key: "save-as",
      label: (
        <SaveAsMenu imgId={id} imgName={imgName} pid={pid} imgMeta={imgMeta} />
      ),
    },
    {
      key: "back-to-project",
      label: (
        <Link href={`/items?itemId=${pid}`}>
          <Button size="small" type="text">
            Back
          </Button>
        </Link>
      ),
    },
  ];
  return (
    <>
      <Dropdown
        menu={{ items }}
        trigger={["click"]}
        overlayClassName={styles.leftTopMenu}
      >
        <Button type="text" icon={<AiOutlineMenu size={20} />} />
      </Dropdown>
      <EnsurePermission permission={PermissionFlag.edit} iidPath={iidPath}>
        <SaveImageViewBtn id={id} img_meta={imgMeta} />
      </EnsurePermission>
    </>
  );
}
