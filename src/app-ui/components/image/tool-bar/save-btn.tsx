import { neonApi } from "@/lib/apis/neon/api";
import { ImgMeta } from "@/lib/apis/neon/image-endpoints";
import { useAppSelector } from "@/lib/app-store/hooks";

import {
  selectGamma,
  useStateMergedChannels,
} from "@/lib/app-store/image-slice";
import { useHandleApiErrorDefault } from "@/lib/utils";
import { App, Button } from "antd";
import { useHotkeys } from "react-hotkeys-hook";
import { AiOutlineSave } from "react-icons/ai";

interface Props {
  id: string;
  img_meta: ImgMeta;
}

export default function SaveImageViewBtn({ id, img_meta }: Props) {
  const app = App.useApp();
  const mergedChannel = useStateMergedChannels(img_meta.channels);
  const [saveImgMeta] = neonApi.useSaveImgMetaMutation();
  const handleApiErrorDefault = useHandleApiErrorDefault();
  const gamma = useAppSelector(selectGamma);

  const saveImageViewChanges = async () => {
    // we only change color here for now
    await saveImgMeta({
      id: id,
      channels: mergedChannel.map((x) => ({
        id: x.id,
        color: x.color,
        view_min: x.min,
        view_max: x.max,
        view_shown: x.shown,
      })),
      view_gamma: gamma,
    })
      .unwrap()
      .catch(handleApiErrorDefault);
  };

  const showSaveImageViewModal = () => {
    app.modal.confirm({
      title: "操作确认",
      content: "请确认是否执行 保存 操作",
      onOk: saveImageViewChanges,
      maskClosable: true,
    });
  };

  useHotkeys("ctrl+s", () => {}, {
    preventDefault: true,
  });

  useHotkeys("ctrl+s", showSaveImageViewModal, {
    keydown: false,
    keyup: true,
    preventDefault: true,
  });

  return (
    <Button
      type="text"
      icon={<AiOutlineSave size={20} />}
      onClick={showSaveImageViewModal}
    />
  );
}
