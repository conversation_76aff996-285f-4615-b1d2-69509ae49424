"use client";
// import InfoPanel from "@/lib/components/image/info-panel/info-panel";
// import ImageMenuArea from "@/lib/components/image/menu-area";
// import Viewer from "@/lib/components/image/viewer/viewer";
// import Zoomer from "@/lib/components/image/zoomer";
import { Flex } from "antd";

type Channel = {
  id: number;
  name: string;
  min: number;
  max: number;
  view_min?: number;
  view_max?: number;
  view_shown?: boolean;
  color?: string;
};

type ImgMeta = {
  tile_size: number;
  base_x: number;
  base_y: number;
  min_level: number;
  max_level: number;
  dtype: string;
  channels: Channel[];
  axes: string;
  shape: number[];
  phys_x?: number;
  phys_x_unit?: string;
  phys_y?: number;
  phys_y_unit?: string;
  phys_z?: number;
  phys_z_unit?: string;
  data_version: string;
  view_gamma?: number;
};

type ImgInfo = {
  id: string;
  name: string;
  imgMeta: ImgMeta;
};

export function ImageEditor({ imgInfo }: { imgInfo: ImgInfo }) {
  return (
    <>
      <Flex style={{ height: "100%", overflowX: "auto" }}>
        <Flex
          flex={"0 0 270px"}
          style={{ overflowX: "auto", padding: 10 }}
          className="border-r border-solid"
        >
          menu
          {/* <ImageMenuArea imgMetaRes={imgMetaRes} /> */}
        </Flex>
        <Flex
          flex={"1 1 auto"}
          style={{ position: "relative", minWidth: "800px", margin: 10 }}
        >
          viewer
          {/* <Viewer imgMeta={imgMetaRes.img_meta} imgId={imgId} /> */}
        </Flex>
        <Flex
          flex={"0 0 260px"}
          style={{ overflowX: "auto", padding: 10 }}
          vertical
          gap={10}
          className="border-l border-solid"
        >
          zoomer
          {/* <Zoomer />
          <InfoPanel
            imgMeta={imgMetaRes.img_meta}
            task={imgMetaRes.task}
            imgId={imgId}
            readonly={!hasEditPermission}
          /> */}
        </Flex>
      </Flex>
    </>
  );
}
