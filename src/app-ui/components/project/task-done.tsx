"use client";

import { PrjOverviewTaskList } from "@/app-ui/components/project/task-list";
import { diverseApi } from "@/lib/apis/diverse/api";
import { TaskStatus } from "@/lib/domain/task/svc";
import { Skeleton } from "antd";

export function PrjOverviewDoneTask({ projectId }: { projectId: string }) {
  const { data = [], isLoading } = diverseApi.useGetTasksQuery({
    projectId,
    status: TaskStatus.Finished,
    length: 4,
  });

  if (isLoading) {
    return <Skeleton active />;
  }

  return (
    <PrjOverviewTaskList
      title="已完成"
      data={data.map((task) => ({
        id: task.id,
        taskType: task.task_type,
        title: "xxxxx",
        taskDate: task.finished_at!,
      }))}
    />
  );
}
