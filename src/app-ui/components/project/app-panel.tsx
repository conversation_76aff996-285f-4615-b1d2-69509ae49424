import { paths } from "@/app/route-path";
import { Tabs, type TabsProps } from "antd";
import Link from "next/link";

function AppIngest({ projectId }: { projectId: string }) {
  return (
    <Link href={paths.project.projectId(projectId).app.ingest.path}>解码</Link>
  );
}

export function PrjAppPanel({ projectId }: { projectId: string }) {
  const items: TabsProps["items"] = [
    {
      key: "apps",
      label: <div className="flex-center  gap-1 ">应用</div>,
      children: <AppIngest projectId={projectId} />,
    },
  ];

  return (
    <Tabs
      defaultActiveKey="apps"
      tabPosition="left"
      items={items}
      className="h-full"
    />
  );
}
