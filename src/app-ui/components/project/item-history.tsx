"use client";

import { PrjOverviewItemList } from "@/app-ui/components/project/item-list";
import { diverseApi } from "@/lib/apis/diverse/api";
import { Skeleton } from "antd";

export function PrjOverviewHistoryItem({ projectId }: { projectId: string }) {
  const { data = [], isLoading } = diverseApi.useGetItemsQuery({
    projectId,
    length: 4,
  });

  if (isLoading) {
    return <Skeleton />;
  }

  return (
    <PrjOverviewItemList
      title="历史记录"
      data={data.map(({ id, name, created_at }) => ({
        id,
        name,
        date: created_at,
      }))}
    />
  );
}
