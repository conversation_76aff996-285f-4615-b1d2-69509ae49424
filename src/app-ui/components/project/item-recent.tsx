"use client";

import { PrjOverviewItemList } from "@/app-ui/components/project/item-list";
import { diverseApi } from "@/lib/apis/diverse/api";
import { Skeleton } from "antd";

export function PrjOverviewRecentItem({ projectId }: { projectId: string }) {
  const { data = [], isLoading } = diverseApi.useGetItemsQuery({
    projectId,
    length: 4,
    sortBy: "updated_at",
  });

  if (isLoading) {
    return <Skeleton />;
  }

  return (
    <PrjOverviewItemList
      title="最近更新"
      data={data.map(({ id, name, updated_at }) => ({
        id,
        name,
        date: updated_at,
      }))}
    />
  );
}
