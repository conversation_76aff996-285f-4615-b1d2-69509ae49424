"use client";

import { addTask } from "@/app-ui/actions/safe-actions/task";
import { dropTargetForElements } from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { Button, Form, Input } from "antd";
import { useEffect, useRef } from "react";

function DropableInput({
  onChange,
  value,
}: {
  value?: string;
  onChange?: (path: string) => void;
}) {
  const ref = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!ref.current) {
      return;
    }

    const cleanup = dropTargetForElements({
      element: ref.current,
      onDrop(args) {
        const node = args.source.data.node as { key: string };
        console.log(node);
        onChange?.(node.key);
      },
    });
    return cleanup;
  }, [onChange]);

  return (
    <div ref={ref} className="opacity-70  text-gray-500">
      {/* <span>{value || "拖拽到此"}</span> */}
      <Input
        placeholder="拖拽到此"
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
      />
    </div>
  );
}

export function IngestTask({ projectId }: { projectId: string }) {
  async function onSubmit({ disk, item }: { disk: string; item: string }) {
    await addTask({
      projectId,
      taskReq: {
        taskType: "ingest",
        taskArgs: {
          projectId,
          sourcePath: disk,
          outputFolderId: item,
          outputName: "test-output-name",
        },
      },
    });
    window.location.reload();
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex-auto p-5">
        <Form onFinish={onSubmit} labelCol={{ span: 3 }}>
          <Form.Item label="云盘文件" name="disk" rules={[{ required: true }]}>
            <DropableInput />
          </Form.Item>

          <Form.Item
            label="数据组路径"
            name="item"
            rules={[{ required: false }]}
          >
            <DropableInput />
          </Form.Item>

          <Form.Item label={null}>
            <Button type="primary" htmlType="submit">
              提交
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
}
