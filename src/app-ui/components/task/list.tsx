"use client";

import { rerunTask } from "@/app-ui/actions/safe-actions/task";
import { paths } from "@/app/route-path";
import type { TasksRes } from "@/lib/query/svc";
import { formatDate } from "@/lib/utils";
import { Button, Table, TableProps } from "antd";
import Link from "next/link";

export function TaskList({
  projectId,
  tasks,
}: {
  projectId: string;
  tasks: TasksRes[];
}) {
  async function onRerunTaskClick(taskId: string) {
    await rerunTask({ projectId, taskId });
  }

  const columns: TableProps<TasksRes>["columns"] = [
    {
      title: "id",
      dataIndex: "id",
    },
    {
      title: "type",
      dataIndex: "task_type",
    },
    {
      title: "task_status",
      dataIndex: "task_status",
    },
    {
      title: "start_at",
      dataIndex: "start_at",
      render(v) {
        return formatDate(v);
      },
    },
    {
      title: "finished_at",
      dataIndex: "finished_at",
      render(v) {
        return formatDate(v);
      },
    },
    {
      title: "操作",
      render(d) {
        return (
          <>
            <Link
              href={paths.project.projectId(projectId).task.taskId(d.id).path}
            >
              详情
            </Link>
            <Button
              onClick={() => {
                onRerunTaskClick(d.id);
              }}
            >
              重试
            </Button>
          </>
        );
      },
    },
  ];

  return <Table rowKey={"id"} columns={columns} dataSource={tasks} />;
}
