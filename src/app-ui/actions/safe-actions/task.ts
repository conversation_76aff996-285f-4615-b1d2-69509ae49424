"use server";

import { authActionClient } from "@/app-ui/actions/client";
import { di } from "@/app-ui/di";
import {
  AddTaskInputSchema,
  RerunTaskInputSchema,
} from "@/lib/domain/task/svc";
export const addTask = authActionClient
  .inputSchema(AddTaskInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const taskSvc = await di.getTaskSvc();
    await taskSvc.add_task_and_run(operator, parsedInput);
  });

export const rerunTask = authActionClient
  .inputSchema(RerunTaskInputSchema)
  .action(async ({ parsedInput, ctx }) => {
    const operator = ctx.session;
    const taskSvc = await di.getTaskSvc();
    await taskSvc.rerunTaskById(operator, parsedInput);
  });
