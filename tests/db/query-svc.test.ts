import { beforeEach, describe, expect, it } from "vitest";
import { getTestPrisma } from "./db-fixture";
import { PrismaClient, Project } from "@prisma/client";
import { QuerySvc } from "@/lib/query/svc";
import {
  TaskRemoteInnerState,
  TaskStatus,
  TaskType,
} from "@/lib/domain/task/svc";
import { ItemTypeFlag } from "@/lib/domain/item/svc";

describe("QuerySvc.tasks", async () => {
  let mockPrj: Project;
  let prisma: PrismaClient;
  let querySvc: QuerySvc;

  beforeEach(async () => {
    prisma = await getTestPrisma();
    querySvc = new QuerySvc({ id: "userId", isTestAccount: true }, prisma);

    const otherPrj = await prisma.project.create({
      data: {
        name: "other-prj",
        created_by_id: "userId",
        updated_by_id: "userId",
      },
    });
    mockPrj = await prisma.project.create({
      data: {
        name: "test-prj",
        created_by_id: "userId",
        updated_by_id: "userId",
      },
    });

    await prisma.task.createMany({
      data: [
        {
          project_id: otherPrj.id,
          task_type: TaskType.ingest,
          task_args: { input: "other-project-data" },
          remote_task_id: "other-remote-1",
          remote_task_status: TaskRemoteInnerState.NIL,
          created_by_id: "userId",
          start_at: new Date("2025-01-01T10:00:00Z"),
        },
        {
          project_id: mockPrj.id,
          task_type: TaskType.ingest,
          task_args: { source: "dataset1.zip", format: "dicom" },
          remote_task_id: "remote-1",
          remote_task_status: TaskRemoteInnerState.NIL,
          created_by_id: "userId",
          start_at: new Date("2025-01-01T09:00:00Z"),
        },
        {
          project_id: mockPrj.id,
          task_type: TaskType.rescale_intensity,
          task_args: { min_val: 0, max_val: 255 },
          remote_task_id: "remote-2",
          remote_task_status: TaskRemoteInnerState.PENDING_ARGS_AVAIL,
          created_by_id: "userId",
          start_at: new Date("2025-01-01T11:00:00Z"),
        },
        {
          project_id: mockPrj.id,
          task_type: TaskType.register,
          task_args: { method: "rigid", metric: "mi" },
          remote_task_id: "remote-3",
          remote_task_status: TaskRemoteInnerState.PENDING_NODE_ASSIGNMENT,
          created_by_id: "userId",
          start_at: new Date("2025-01-01T12:00:00Z"),
        },
        {
          project_id: mockPrj.id,
          task_type: TaskType.export,
          task_args: { format: "nifti", compression: true },
          remote_task_id: "remote-4",
          remote_task_status: TaskRemoteInnerState.FINISHED,
          created_by_id: "userId",
          start_at: new Date("2025-01-01T06:00:00Z"),
          finished_at: new Date("2025-01-01T06:30:00Z"),
        },
        {
          project_id: mockPrj.id,
          task_type: TaskType.save_as,
          task_args: { name: "processed_data", location: "/exports" },
          remote_task_id: "remote-5",
          remote_task_status: TaskRemoteInnerState.FINISHED,
          created_by_id: "userId",
          start_at: new Date("2025-01-01T07:00:00Z"),
          finished_at: new Date("2025-01-01T07:30:00Z"),
        },
        {
          project_id: mockPrj.id,
          task_type: TaskType.clip_intensity,
          task_args: { lower: 10, upper: 90 },
          remote_task_id: "remote-6",
          remote_task_status: TaskRemoteInnerState.FINISHED,
          created_by_id: "userId",
          start_at: new Date("2025-01-01T08:00:00Z"),
          finished_at: new Date("2025-01-01T08:45:00Z"),
        },
        {
          project_id: mockPrj.id,
          task_type: TaskType.register,
          task_args: { method: "affine" },
          remote_task_id: "remote-7",
          remote_task_status: TaskRemoteInnerState.FAILED,
          created_by_id: "userId",
          start_at: new Date("2025-01-01T05:00:00Z"),
          finished_at: new Date("2025-01-01T05:15:00Z"),
          error: "Registration failed: insufficient contrast",
        },
        {
          project_id: mockPrj.id,
          task_type: TaskType.ingest,
          task_args: { source: "corrupted.zip" },
          remote_task_id: "remote-8",
          remote_task_status: TaskRemoteInnerState.FAILED,
          created_by_id: "userId",
          start_at: new Date("2025-01-01T04:00:00Z"),
          finished_at: new Date("2025-01-01T04:10:00Z"),
          error: "File corruption detected",
        },
        {
          project_id: mockPrj.id,
          task_type: TaskType.clip_intensity,
          task_args: { threshold: 0.5 },
          remote_task_id: null,
          remote_task_status: null,
          created_by_id: "userId",
          start_at: new Date("2025-01-01T03:00:00Z"),
        },
        {
          project_id: mockPrj.id,
          task_type: TaskType.export,
          task_args: { format: "png" },
          remote_task_id: "remote-9",
          remote_task_status: TaskRemoteInnerState.FINISHED,
          created_by_id: "userId",
          start_at: new Date("2025-01-01T07:00:00Z"),
          finished_at: new Date("2025-01-01T07:20:00Z"),
        },
        {
          project_id: mockPrj.id,
          task_type: TaskType.rescale_intensity,
          task_args: { auto_scale: true },
          remote_task_id: "remote-10",
          remote_task_status: TaskRemoteInnerState.PENDING_ARGS_AVAIL,
          created_by_id: "userId",
          start_at: new Date("2025-01-01T13:00:00Z"),
        },
      ],
    });
  });

  it("should filter tasks by status", async () => {
    const [runningTasks, finishedTasks, failedTasks] = await Promise.all([
      querySvc.tasks({ projectId: mockPrj.id, status: TaskStatus.Running }),
      querySvc.tasks({ projectId: mockPrj.id, status: TaskStatus.Finished }),
      querySvc.tasks({ projectId: mockPrj.id, status: TaskStatus.Failed }),
    ]);

    expect([
      runningTasks.length,
      finishedTasks.length,
      failedTasks.length,
    ]).toEqual([4, 4, 2]);
    expect(
      runningTasks.every(
        (task) =>
          task.task_status === TaskStatus.Running &&
          task.project_id === mockPrj.id
      )
    ).toBe(true);
    expect(
      finishedTasks.every(
        (task) =>
          task.task_status === TaskStatus.Finished && task.finished_at !== null
      )
    ).toBe(true);
    expect(
      failedTasks.every(
        (task) => task.task_status === TaskStatus.Failed && task.error !== null
      )
    ).toBe(true);
  });

  it("should sort and limit tasks correctly", async () => {
    const [latestTasks, earliestTasks, finishedByTime] = await Promise.all([
      querySvc.tasks({
        projectId: mockPrj.id,
        sortBy: "start_at",
        order: "desc",
        length: 3,
      }),
      querySvc.tasks({
        projectId: mockPrj.id,
        sortBy: "start_at",
        order: "asc",
        length: 3,
      }),
      querySvc.tasks({
        projectId: mockPrj.id,
        status: TaskStatus.Finished,
        sortBy: "finished_at",
        order: "desc",
        length: 2,
      }),
    ]);

    expect([
      latestTasks.length,
      earliestTasks.length,
      finishedByTime.length,
    ]).toEqual([3, 3, 2]);
    expect(latestTasks[0].start_at).toEqual(new Date("2025-01-01T13:00:00Z"));
    expect(earliestTasks[0].start_at).toEqual(new Date("2025-01-01T03:00:00Z"));
    expect(
      finishedByTime[0].finished_at! >= finishedByTime[1].finished_at!
    ).toBe(true);
  });

  it("should isolate tasks by project", async () => {
    const [targetProjectTasks, nonExistentTasks] = await Promise.all([
      querySvc.tasks({ projectId: mockPrj.id }),
      querySvc.tasks({ projectId: "non-existent-project-id" }),
    ]);

    expect([targetProjectTasks.length, nonExistentTasks.length]).toEqual([
      11, 0,
    ]);
    expect(
      targetProjectTasks.every((task) => task.project_id === mockPrj.id)
    ).toBe(true);
  });

  it("should handle edge cases and data integrity", async () => {
    const [limitedTasks, failedWithError, allTasks] = await Promise.all([
      querySvc.tasks({ projectId: mockPrj.id, length: 100 }),
      querySvc.tasks({ projectId: mockPrj.id, status: TaskStatus.Failed }),
      querySvc.tasks({ projectId: mockPrj.id }),
    ]);

    expect(limitedTasks).toHaveLength(11);

    const hasExpectedErrors =
      failedWithError.some((task) =>
        task.error?.includes("insufficient contrast")
      ) &&
      failedWithError.some((task) => task.error?.includes("File corruption"));
    expect(hasExpectedErrors).toBe(true);

    const localTasks = allTasks.filter((task) => task.remote_task_id === null);
    expect([localTasks.length, localTasks[0]?.task_status]).toEqual([1, null]);

    expect(
      allTasks.every(
        (task) =>
          task.project_id === mockPrj.id &&
          task.created_by_id === "userId" &&
          task.start_at instanceof Date &&
          task.hasOwnProperty("task_type") &&
          task.hasOwnProperty("task_args")
      )
    ).toBe(true);
  });

  it("should return complete task data structure", async () => {
    const task = (
      await querySvc.tasks({ projectId: mockPrj.id, length: 1 })
    )[0];

    expect(task).toMatchObject({
      id: expect.any(String),
      created_by_id: "userId",
      task_status: expect.any(String),
      project_id: mockPrj.id,
      task_type: expect.any(String),
      task_args: expect.any(Object),
      start_at: expect.any(Date),
    });
    expect(task).not.toHaveProperty("extra");
  });
});

describe("QuerySvc.items", async () => {
  let mockPrj: Project;
  let otherPrj: Project;
  let prisma: PrismaClient;
  let querySvc: QuerySvc;

  beforeEach(async () => {
    prisma = await getTestPrisma();
    querySvc = new QuerySvc({ id: "userId", isTestAccount: true }, prisma);

    mockPrj = await prisma.project.create({
      data: {
        name: "test-project",
        created_by_id: "userId",
        updated_by_id: "userId",
      },
    });
    otherPrj = await prisma.project.create({
      data: {
        name: "other-project",
        created_by_id: "userId",
        updated_by_id: "userId",
      },
    });

    await prisma.item.createMany({
      data: [
        {
          name: "file1.jpg",
          piid_path: `/${mockPrj.id}/`,
          iid: 1,
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T09:00:00Z"),
          updated_at: new Date("2025-01-01T10:00:00Z"),
          deleted_at: 0,
        },
        {
          name: "file2.png",
          piid_path: `/${mockPrj.id}/`,
          iid: 2,
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T10:00:00Z"),
          updated_at: new Date("2025-01-01T09:00:00Z"),
          deleted_at: 0,
        },
        {
          name: "file3.pdf",
          piid_path: `/${mockPrj.id}/`,
          iid: 3,
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          created_at: new Date("2025-01-01T08:00:00Z"),
          updated_at: new Date("2025-01-01T11:00:00Z"),
          deleted_at: 0,
        },
        {
          name: "folder1",
          piid_path: `/${mockPrj.id}/`,
          iid: 4,
          type_flag: ItemTypeFlag.Folder,
          created_by_id: "userId",
          updated_by_id: "userId",
          deleted_at: 0,
        },
        {
          name: "deleted-file.txt",
          piid_path: `/${mockPrj.id}/`,
          iid: 5,
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          deleted_at: 1,
        },
        {
          name: "other-file.doc",
          piid_path: `/${otherPrj.id}/`,
          iid: 6,
          type_flag: ItemTypeFlag.File,
          created_by_id: "userId",
          updated_by_id: "userId",
          deleted_at: 0,
        },
      ],
    });
  });

  it("should filter and return only files from project", async () => {
    const items = await querySvc.items({ projectId: mockPrj.id });
    expect(items).toHaveLength(3);
    expect(
      items.every(
        (item) => item.type_flag === ItemTypeFlag.File && item.deleted_at === 0
      )
    ).toBe(true);
    expect(items.map((item) => item.name)).toEqual(
      expect.not.arrayContaining(["folder1", "deleted-file.txt"])
    );
  });

  it("should sort correctly", async () => {
    const byCreatedAt = await querySvc.items({ projectId: mockPrj.id });
    expect(byCreatedAt[0].name).toBe("file2.png");

    const byUpdatedAt = await querySvc.items({
      projectId: mockPrj.id,
      sortBy: "updated_at",
    });
    expect(byUpdatedAt[0].name).toBe("file3.pdf");
  });

  it("should take right count of items", async () => {
    const [limitedItems, mockPrjItems, otherPrjItems] = await Promise.all([
      querySvc.items({ projectId: mockPrj.id, length: 2 }),
      querySvc.items({ projectId: mockPrj.id, length: 99 }),
      querySvc.items({ projectId: otherPrj.id, length: 99 }),
    ]);

    expect([
      limitedItems.length,
      mockPrjItems.length,
      otherPrjItems.length,
    ]).toEqual([2, 3, 1]);
    expect(otherPrjItems[0].name).toBe("other-file.doc");
  });

  it("should return empty array if project not found", async () => {
    const [nonExistentItems, largeLength] = await Promise.all([
      querySvc.items({ projectId: "non-existent-id" }),
      querySvc.items({ projectId: mockPrj.id, length: 100 }),
    ]);

    expect([nonExistentItems.length, largeLength.length]).toEqual([0, 3]);
  });

  it("should return complete data structure", async () => {
    const item = (
      await querySvc.items({ projectId: mockPrj.id, length: 1 })
    )[0];

    expect(item).toMatchObject({
      id: expect.any(String),
      name: expect.any(String),
      type_flag: ItemTypeFlag.File,
      created_by_id: "userId",
      deleted_at: 0,
    });
    expect(item).not.toHaveProperty("iid");
    expect(item).not.toHaveProperty("piid_path");
  });
});
