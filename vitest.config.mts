import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";
import { defineConfig } from "vitest/config";

import nextjsEnv from "@next/env";
nextjsEnv.loadEnvConfig(process.cwd());

export default defineConfig({
  plugins: [tsconfigPaths(), react()],
  test: {
    environment: "jsdom",
    setupFiles: "vitest-setup.ts",
    server: {
      deps: {
        inline: ["next-auth"],
      },
    },
    projects: [
      {
        extends: true,
        test: {
          name: "unit-test",
          includeSource: ["src/**/*.ts"],
          include: [],
        },
      },
      {
        extends: true,
        test: {
          name: "db-test",
          include: ["tests/db/*.test.ts"],
        },
      },
    ],
    coverage: {
      include: ["src/lib/**/*.ts"],
      thresholds: {
        branches: 93.75,
        functions: 26.69,
        lines: 48.14,
        statements: 48.14,
        autoUpdate: true,
      },
    },
  },
  define: {
    "import.meta.vitest": "undefined", // might not be needed as we are using nextjs
  },
});